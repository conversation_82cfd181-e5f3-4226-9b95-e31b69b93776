import { z } from 'zod';

export const LOGIN_USER_FORM_SCHEMA = z.object({
  email: z.string().toLowerCase().min(1, 'email-required').email({
    message: 'email-invalid',
  }),
  password: z.string().min(1, 'password-required'),
});

export const RESET_USER_PASSWORD_FORM_SCHEMA = z.object({
  currentPassword: z.string().min(1, 'required'),
  newPassword: z.string().min(1, 'required'),
  confirmPassword: z.string().min(1, 'required'),
});

export const COMPLETE_USER_REGISTRATION_FORM_SCHEMA = z.object({
  email: z.string().min(1, 'email-required').email({
    message: 'email-invalid',
  }),
  temporaryPassword: z.string().trim().min(1, 'temp-password-required'),
  newPassword: z.string().min(1, 'new-password-required'),
  confirmPassword: z.string().min(1, 'confirm-new-password-required'),
});

export const ACCEPT_INVITATION_FORM_SCHEMA = z.object({
  newPassword: z.string().min(1, 'new-password-required'),
  confirmPassword: z.string().min(1, 'confirm-new-password-required'),
});

export const DEFAULT_CHECKOUT_FORM_SCHEMA = z.object({
  email: z.string().min(1, 'email-required').email({
    message: 'email-invalid',
  }),
  fullName: z.string().trim().min(1, 'full-name-required'),
});

export const SIGNUP_USER_FORM_SCHEMA = z.object({
  email: z.string().min(1, 'email-required').email({
    message: 'email-invalid',
  }),
  firstName: z.string().trim().min(1, 'required'),
  lastName: z.string().trim().min(1, 'required'),
  password: z.string().min(1, 'password-required'),
  confirmPassword: z.string().min(1, 'confirm-password-required'),
  language: z.string().trim().min(1, 'required'),
});

export const PROFILE_FORM_SCHEMA = z.object({
  firstName: z.string().min(1, 'first-name-required'),
  lastName: z.string().min(1, 'last-name-required'),
  dateOfBirth: z.date().optional().nullable(),
  phoneNumber: z.string().optional().nullable(),
  organisation: z.string().optional().nullable(),
  affiliation: z.string().optional().nullable(),
  profilePicture: z.string().optional().nullable(),
});

const PROFILE_ADDRESS_SCHEMA = z.object({
  addressLine1: z.string().min(1, 'required'),
  addressLine2: z.string().optional().nullable(),
  city: z.string().min(1, 'required'),
  state: z.string().optional().nullable(),
  postcode: z.string().min(1, 'required'),
  country: z.string().min(1, 'required'),
});

export const ADD_PROFILE_RECEIPT_BILLING_FORM_SCHEMA = z.object({
  address: PROFILE_ADDRESS_SCHEMA,
});

export const ADD_PROFILE_INVOICE_BILLING_FORM_SCHEMA = z.object({
  address: PROFILE_ADDRESS_SCHEMA,
  vatNumber: z.string().min(1, 'required'),
});

export const TEACHER_INVITE_DATA_SCHEMA = z.object({
  email: z.string().min(1, 'required').email('invalid-email'),
  firstName: z.string().trim().min(1, 'required'),
  lastName: z.string().trim().min(1, 'required'),
});

export const INVITE_TEACHERS_FORM_SCHEMA = z.object({
  teachers: z.array(TEACHER_INVITE_DATA_SCHEMA),
});

export const ADD_CLASS_FORM_SCHEMA = z.object({
  name: z.string().min(1, 'required'),
  grade: z.string().min(1, 'required'),
});

export const ADD_GROUP_FORM_SCHEMA = z.object({
  name: z.string().min(1, 'required'),
  description: z.string().optional(),
});

export const USER_FORM_SCHEMA = z.object({
  firstName: z.string().trim().min(1, 'first-name-required'),
  lastName: z.string().trim().min(1, 'last-name-required'),
  role: z.string().trim().min(1, 'required'),
  language: z.string().trim().optional(),
  email: z.string().trim().min(1, 'email-required').email({
    message: 'email-invalid',
  }),
  certificatesAndLicenses: z
    .array(
      z.object({
        type: z.string(),
        licenses: z.number().optional(),
        progress: z.string().optional(),
      })
    )
    .optional(),
});

export const SCHOOL_FORM_SCHEMA = z.object({
  name: z.string().trim().min(1, 'required'),
  code: z.string().trim().min(1, 'required'),
  addressLine1: z.string().trim().min(1, 'address-required'),
  city: z.string().trim().min(1, 'city-required'),
  postcode: z.string().trim().min(1, 'postcode-required'),
  country: z.string().trim().min(1, 'country-required'),
  profilePicture: z.string().optional().nullable(),
  email: z.string().trim().min(1, 'required').email({
    message: 'email-invalid',
  }),
  vatNumber: z.string().trim(),
  phoneNumber: z.string().trim().optional(),
  url: z.string().trim().optional(),
  administrator: z.string().optional().nullable(),
  type: z.string().min(1, 'required'),
});
