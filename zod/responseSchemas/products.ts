import { z } from 'zod';

import { ZOD_MATH_PRO_AVAILABLE_PRODUCTS_TYPE } from '../zodConstants';

export const PRODUCT_DETAILS_SCHEMA = z.object({
  stripeId: z.string(),
  kind: z.enum(['certificate', 'test', 'ebook', 'licenses']),
  name: z.string(),
  price: z.object({
    // will always be in cents for example 1 euro is 100 cents
    amount: z.number(),
    currency: z.enum(['usd', 'eur', 'gbp']),
    totalAmount: z.number().optional(),
    taxAmount: z.number().optional(),
    taxRate: z.string().optional(),
  }),
  images: z.array(z.string()),
  type: ZOD_MATH_PRO_AVAILABLE_PRODUCTS_TYPE,
});

export const CERTIFICATIONS_PRODUCTS_SCHEMA = z.array(
  PRODUCT_DETAILS_SCHEMA.extend({
    language: z.string(),
    progress: z.optional(
      z.object({
        id: z.string(),
        modules: z.array(
          z.object({
            order: z.number(),
            count: z.number(),
            answered: z.number(),
          })
        ),
      })
    ),
  })
);

export const LICENSES_PRODUCT_SCHEMA = z.array(
  PRODUCT_DETAILS_SCHEMA.extend({
    quantity: z.string(),
  })
);
