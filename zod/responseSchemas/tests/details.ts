import { z } from 'zod';

import { ZOD_MATH_PRO_AVAILABLE_PRODUCTS_TYPE } from '@/zod/zodConstants';

import { PRODUCT_DETAILS_SCHEMA } from '../products';

export const TEST_DETAILS_SCHEMA = z.object({
  name: z.string(),
  description: z.string(),
  products: z.array(PRODUCT_DETAILS_SCHEMA),
});

export const ALL_APPLICATION_TESTS_SCHEMA = z.array(
  z.object({
    type: ZOD_MATH_PRO_AVAILABLE_PRODUCTS_TYPE,
    locked: z.boolean(),
    research: z.boolean(),
  })
);
