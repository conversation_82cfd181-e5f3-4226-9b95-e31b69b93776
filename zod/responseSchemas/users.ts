import { z } from 'zod';

import { ZOD_MATH_PRO_AVAILABLE_PRODUCTS_TYPE } from '../zodConstants';

export const USER_DETAILS_SCHEMA = z.object({
  id: z.string(),
  username: z.string().email(),
  roles: z.array(
    z.enum([
      'teacher',
      'schoolTeacher',
      'schoolAdmin',
      'admin',
      'developer',
      'studyAdmin',
      'student',
      'researcher',
    ])
  ),
  vatNumber: z.string().nullable().optional(),
  profile: z.object({
    firstName: z.string(),
    lastName: z.string(),
    email: z.string().email(),
    phoneNumber: z.string().nullable(),
    dob: z.optional(z.string().nullable()),
    organisation: z.optional(z.string().nullable()),
    onboarded: z.optional(z.boolean()),
    language: z.string(),
    affiliation: z.string().optional().nullable(),
    profilePicture: z.string().optional().nullable(),
    address: z
      .object({
        addressLine1: z.string(),
        addressLine2: z.string().nullable(),
        city: z.string(),
        state: z.optional(z.string()).nullable(),
        postcode: z.string(),
        country: z.string(),
      })
      .nullable(),
    certificates: z.array(
      z.object({
        type: ZOD_MATH_PRO_AVAILABLE_PRODUCTS_TYPE,
        version: z.number(),
        progress: z.optional(
          z.object({
            id: z.string(),
            completed: z.optional(z.string().nullable()),
          })
        ),
      })
    ),
    vatNumber: z.string().nullable().optional(),
    licenses: z
      .array(
        z.object({
          type: z.string(),
          remaining: z.number(),
        })
      )
      .optional()
      .nullable(),
  }),
  school: z
    .object({
      id: z.string(),
      name: z.string(),
      code: z.string(),
      email: z.string().email(),
    })
    .optional(),
});

export const USER_OWNS_PRODUCT_SCHEMA = z.object({
  ownsProduct: z.boolean(),
});
