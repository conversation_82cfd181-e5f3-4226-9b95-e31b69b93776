import { Text as MantineText } from '@mantine/core';
import React from 'react';
import { useTranslation } from 'react-i18next';

import baseTheme from '@/styles/baseTheme';
import { ColorsType, TranslationKeysType } from '@/types/common';

import { BASE_BOLD_FONT_WEIGHT, TEXT_TYPES } from './consts';

type TextPropsType = {
  type?: keyof typeof TEXT_TYPES;
  color?: ColorsType;
  transKey?: TranslationKeysType;
  transVariables?: Record<string, string>;
  untranslatedText?: string | number;
  isBold?: boolean;
  isTruncated?: boolean;
  lineClamp?: number;
  align?: 'center' | 'left' | 'right';
  hasUserSelect?: boolean;
  lh?: number;
  fw?: number;
  mt?: number;
  mb?: number;
  mr?: number;
  ml?: number;
  onClick?: () => void;
  className?: string;
};

const Text = ({
  align = 'left',
  className,
  color = 'black',
  fw = 0,
  hasUserSelect = true,
  isBold = false,
  isTruncated = false,
  // 1.2 line height is the default line height for the desktop browsers
  lh = 1.3,
  lineClamp = 0,
  mb = 0,
  ml = 0,
  mr = 0,
  mt = 0,
  onClick,
  transKey,
  transVariables,
  type = 'body1',
  untranslatedText,
}: TextPropsType) => {
  const { t } = useTranslation();

  return (
    <MantineText
      fw={fw || (isBold ? BASE_BOLD_FONT_WEIGHT : TEXT_TYPES[type].fontWeight)}
      size={TEXT_TYPES[type].fontSize}
      c={baseTheme.colors[color]}
      truncate={isTruncated}
      lh={lh}
      lineClamp={lineClamp}
      {...(TEXT_TYPES[type]?.component && {
        component: TEXT_TYPES[type].component,
      })}
      mb={mb}
      mt={mt}
      mr={mr}
      ml={ml}
      onClick={onClick}
      style={{
        transition: 'all 0.1s ease-out',
        textAlign: align,
        userSelect: hasUserSelect ? 'auto' : 'none',
        minHeight: 'fit-content',
      }}
      className={className}
    >
      {untranslatedText || t(`${transKey}`, transVariables)}
    </MantineText>
  );
};

export default Text;
