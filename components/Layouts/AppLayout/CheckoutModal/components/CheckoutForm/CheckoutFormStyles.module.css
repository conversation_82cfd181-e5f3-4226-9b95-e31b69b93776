.wrapper {
  display: flex;
  flex-direction: column;
}

.card {
  display: flex;
  flex-direction: column;
  width: 100%;
  max-width: 1222px;

  @media screen and (max-width: 1211px) {
    max-width: 600px;
    margin: 0 auto;
  }
}

.header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: var(--spacing-2xl);
}

.headerActions {
  display: flex;
  gap: var(--spacing-lg);
}

.body {
  display: flex;
  justify-content: space-between;
  gap: var(--spacing-xl);
  flex-wrap: wrap;
}

.cardDetails {
  width: 100%;
  max-width: 622px;
}

.summary {
  display: flex;
  flex-direction: column;
  width: 100%;
  max-width: 453px;

  @media screen and (max-width: 1211px) {
    max-width: 100%;
  }
}

.summaryDetails {
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  align-items: space-between;
  height: 100%;
}

.summaryListWrapper {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-smd);
  padding: 0 var(--spacing-smd);
}

.summaryItem {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.itemDetails {
  display: flex;
  align-items: center;
  gap: var(--spacing-md);
}

.summaryFooter {
  display: flex;
  justify-content: flex-end;
  align-items: center;
  gap: var(--spacing-lg);
  margin-top: var(--spacing-2xl);
}

.totalValueWrapper {
  display: flex;
  flex-direction: column;
  align-items: flex-end;
}

.stripeWrapper {
  height: 230px;
  padding: 1px;
  width: 100%;
  max-width: 590px;

  overflow: hidden;

  @media screen and (max-width: 364px) {
    height: 300px;
  }
}
