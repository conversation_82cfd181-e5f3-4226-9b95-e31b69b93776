import { useMutation, useQueryClient } from '@tanstack/react-query';
import { useRouter } from 'next/router';
import React, { useEffect, useMemo, useState } from 'react';
import { useTranslation } from 'react-i18next';
import { BiLogOutCircle } from 'react-icons/bi';

import {
  APP_LAYOUT_ROUTES,
  MAIN_APP_ROUTE,
  PUBLIC_ROUTES,
} from '@/common/routes';
import Icon from '@/components/Icon/Icon';
import LoadingScreen from '@/components/LoadingScreen/LoadingScreen';
import AlertDialog from '@/components/Modals/AlertDialog/AlertDialog';
import NavigationEvents from '@/components/NavigationEvents/NavigationEvents';
import Text from '@/components/Text/Text';
import { TestsContext } from '@/context/TestsProvider';
import { UserContext } from '@/context/UserProvider';
import AUTH from '@/services/auth';
import { DashboardRoutesType } from '@/types/common';

import s from './AppLayout.module.css';
import CheckoutModal from './CheckoutModal/CheckoutModal';
import NavigationItem from './NavigationItem/NavigationItem';
import { LayoutRouteType } from './types';

const DASHBOARD_PROFILE: LayoutRouteType = {
  id: 7,
  route: '/dashboard/profile',
  queryParams: '?tab=account',
  name: 'profile',
  icon: {
    name: 'AccountSimpleSvg',
    height: 30,
    width: 30,
  },
};

const AppLayout = ({ children }: { children: React.ReactNode }) => {
  const { t } = useTranslation();
  const { isSchoolRole, isUserLoading, user, userRoles } = UserContext();
  const { allTests, areTestsLoading } = TestsContext();
  const router = useRouter();
  const selectedRouteFromUrl = router.query.route as DashboardRoutesType;
  const [selectedRoute, setSelectedRoute] = useState(
    `${MAIN_APP_ROUTE}/${selectedRouteFromUrl}`
  );
  const [isLogoutModalOpen, setIsLogoutModalOpen] = useState(false);
  const queryClient = useQueryClient();

  const DASHBOARD_NAVIGATION_ITEMS: (LayoutRouteType & {
    isVisible: boolean;
  })[] = useMemo(() => {
    return [
      {
        id: 1,
        route: `/dashboard/${APP_LAYOUT_ROUTES.CERTIFICATION_TESTS}`,
        name: 'tests',
        isVisible: true,
        icon: {
          name: 'TestSvg',
          height: 28,
          width: 24,
        },
      },
      {
        id: 2,
        route: `/dashboard/${APP_LAYOUT_ROUTES.STUDENTS}`,
        name: 'Students',
        isVisible: !isSchoolRole,
        icon: {
          name: 'StudentSvg',
          height: 30,
          width: 28,
        },
      },
      {
        id: 3,
        route: `/dashboard/${APP_LAYOUT_ROUTES.SCHOOL}`,
        name: 'school',
        isVisible: isSchoolRole,
        icon: {
          name: 'StudentSvg',
          height: 30,
          width: 28,
        },
      },
      {
        id: 4,
        route: `/dashboard/${APP_LAYOUT_ROUTES.STORE}`,
        queryParams: '?tab=certifications',
        name: 'store',
        isVisible: userRoles.isIndependentTeacher || userRoles.isSchoolAdmin,
        icon: {
          name: 'CartSvg',
          height: 22,
          width: 28,
        },
      },
      {
        id: 5,
        route: '/dashboard/e-learning',
        name: 'e-learning',
        isVisible: !userRoles.isResearcher,
        icon: {
          name: 'BookSvg',
          height: 26,
          width: 32,
        },
      },
      {
        id: 8,
        route: `/dashboard/${APP_LAYOUT_ROUTES.ADMIN}`,
        name: 'admin',
        isVisible: userRoles.isAdmin,
        icon: {
          name: 'SettingsSvg',
          height: 30,
          width: 27,
        },
      },
      {
        id: 6,
        route: `/dashboard/${APP_LAYOUT_ROUTES.ASSISTANT}`,
        name: 'assistant',
        isVisible: userRoles.isDeveloper,
        icon: {
          name: 'ChatSvg',
          height: 27,
          width: 27,
        },
      },
      // {
      //   id: 7,
      //   route: '/dashboard/docs',
      //   name: 'docs',
      //   isVisible: userRoles.isDeveloper,
      //   icon: {
      //     name: 'DocsSvg',
      //     height: 26,
      //     width: 22,
      //   },
      // },
    ];
  }, [isSchoolRole, userRoles]);

  const logoutMutation = useMutation({
    mutationFn: AUTH.LOGOUT_USER,
    onSuccess: () => {
      router.push(PUBLIC_ROUTES.HOME);
      queryClient.clear();
    },
  });

  useEffect(() => {
    setSelectedRoute(`${MAIN_APP_ROUTE}/${selectedRouteFromUrl}`);
  }, [selectedRouteFromUrl]);

  if (!router.isReady) return null;

  return isUserLoading || areTestsLoading ? (
    <LoadingScreen />
  ) : (
    <div className={s.wrapper}>
      <NavigationEvents />

      <div className={s['side-menu-wrapper']}>
        <div className={s['logo-wrapper']}>
          <Icon name="LogoColorfulSvg" className={s.icon} />
        </div>

        <div className={s['navigation-list']}>
          <div className={s['navigation-items']}>
            {DASHBOARD_NAVIGATION_ITEMS.map((item) => {
              const isSelected = selectedRoute === item.route;

              if (!item.isVisible) return null;

              return (
                <NavigationItem
                  key={item.route}
                  name={t(item.name)}
                  isSelected={isSelected}
                  onClick={() => {
                    if (
                      item.route ===
                      `/dashboard/${APP_LAYOUT_ROUTES.CERTIFICATION_TESTS}`
                    )
                      router.push(
                        `${item.route}${allTests[0] ? `?tab=${allTests[0].type}` : ''}`
                      );
                    else router.push(`${item.route}${item.queryParams || ''}`);
                  }}
                  icon={{
                    name: item.icon.name,
                    height: item.icon.height,
                    width: item.icon.width,
                  }}
                />
              );
            })}
          </div>

          <div>
            <NavigationItem
              name={user?.profile?.firstName || ''}
              isSelected={selectedRoute === DASHBOARD_PROFILE.route}
              onClick={() => {
                if (
                  userRoles.isSchoolAdmin ||
                  userRoles.isIndependentTeacher ||
                  userRoles.isResearcher
                ) {
                  router.push(
                    `${DASHBOARD_PROFILE.route}${DASHBOARD_PROFILE.queryParams || ''}`
                  );
                } else {
                  router.push(`${DASHBOARD_PROFILE.route}`);
                }
              }}
              icon={{
                name: DASHBOARD_PROFILE.icon.name,
                height: DASHBOARD_PROFILE.icon.height,
                width: DASHBOARD_PROFILE.icon.width,
              }}
            />

            <div className={s['logout-wrapper']}>
              <div className={s.divider} />

              <div className={s['logout-items']}>
                <div className={s.logoutIcon}>
                  <BiLogOutCircle
                    size={32}
                    onClick={() => setIsLogoutModalOpen(true)}
                  />
                </div>

                <Text
                  transKey="logout"
                  type="button"
                  color="gray400"
                  className={s.logout}
                  onClick={() => setIsLogoutModalOpen(true)}
                />
              </div>
            </div>
          </div>
        </div>
      </div>

      <div className={s.container}>{children}</div>

      <AlertDialog
        isOpen={isLogoutModalOpen}
        onConfirmAction={() => {
          logoutMutation.mutate();
        }}
        title="logout-confirmation"
        description="logout-confirmation-description"
        onCancel={() => setIsLogoutModalOpen(false)}
        isActionInProgress={logoutMutation.isPending}
        variant="danger"
      />

      <CheckoutModal />
    </div>
  );
};

export default AppLayout;
