/* eslint-disable jsx-a11y/click-events-have-key-events */
/* eslint-disable jsx-a11y/no-static-element-interactions */
import { Divider, Flex, LoadingOverlay, ScrollArea } from '@mantine/core';
import { useQueries, useQuery } from '@tanstack/react-query';
import { getLangNameFromCode } from 'language-name-map';
import React from 'react';
import { useTranslation } from 'react-i18next';
import { FaLaptop } from 'react-icons/fa';
import { IoPeople } from 'react-icons/io5';

import { PRODUCTS, SUPPORTED_SCHOOL_GRADES } from '@/common/consts';
import QUERY_KEYS from '@/common/queryKeys';
import Button from '@/components/Button/Button';
import Card from '@/components/Card/Card';
import CircleButtonSelector from '@/components/CircleButtonSelector/CircleButtonSelector';
import CloseButton from '@/components/CloseButton/CloseButton';
import DisplayCloseTag from '@/components/DisplayCloseTag/DisplayCloseTag';
import Icon from '@/components/Icon/Icon';
import Radio from '@/components/Radio/Radio';
import SelectDropdown from '@/components/SelectDropdown/SelectDropdown';
import Text from '@/components/Text/Text';
import { TestsContext } from '@/context/TestsProvider';
import { UserContext } from '@/context/UserProvider';
import TESTS_DETAILS from '@/services/tests/details';
import TESTS_SESSIONS_CREATE from '@/services/tests/sessions-create';
import {
  AllApplicationTestsDetailsType,
  CanCreateSessionTestType,
} from '@/types/common';

import { HandleSelectedDataUpdateType, SelectedDataType } from '../types';
import s from './SelectTestScreen.module.css';

type SelectTestScreenProps = {
  isUserAdminOrDeveloper: boolean;
  selectedData: SelectedDataType;
  onCloseModal: () => void;
  onSelectParticipantsClick: () => void;
  handleSelectedDataUpdate: HandleSelectedDataUpdateType;
  onConductButtonClick: () => void;
  // Enable when schedule feature is wanted
  // onScheduleButtonClick: () => void;
  isActionButtonLoading: boolean;
};

const SelectTestScreen = ({
  handleSelectedDataUpdate,
  isActionButtonLoading,
  isUserAdminOrDeveloper,
  onCloseModal,
  onConductButtonClick,
  // Enable when schedule feature is wanted
  // onScheduleButtonClick,
  onSelectParticipantsClick,
  selectedData,
}: SelectTestScreenProps) => {
  const { i18n, t } = useTranslation();

  const {
    user,
    userRoles: { isResearcher },
  } = UserContext();

  // The first call that runs to check if the user is allowed to create a test session
  const { data: permissionData, isLoading: isPermissionLoading } =
    useQuery<CanCreateSessionTestType | null>({
      queryKey: [
        QUERY_KEYS.CREATE_SESSION_TEST_CHECK,
        selectedData.testType,
        user.id,
      ],
      queryFn: TESTS_SESSIONS_CREATE.CAN_CREATE_TEST_SESSION,
    });

  const { data: allTests, isLoading: areTestsLoading } =
    useQuery<AllApplicationTestsDetailsType | null>({
      queryFn: () => TESTS_DETAILS.GET_ALL_APPLICATION_TESTS(true),
      queryKey: [QUERY_KEYS.ALL_APPLICATION_TESTS_FILTERED],
      enabled: Boolean(permissionData?.result),
    });

  const queries = useQueries({
    queries: [
      {
        queryKey: [
          QUERY_KEYS.SUBTESTS_LIST_BY_TEST_TYPE,
          selectedData.testType,
          selectedData.testLevel,
          selectedData.language,
        ],
        queryFn: () =>
          selectedData.testType
            ? TESTS_DETAILS.GET_SUB_TESTS_BY_TEST_TYPE({
                testType: selectedData.testType,
                grade: selectedData?.testLevel || null,
                language: selectedData.language || i18n.language,
              })
            : [],
        staleTime: 1000 * 60 * 60,
        enabled:
          Boolean(selectedData.testType) &&
          Boolean(selectedData?.testLevel) &&
          Boolean(permissionData?.result),
      },
      {
        queryKey: [
          QUERY_KEYS.TEST_TYPE_SUPPORTED_LANGUAGE_LIST,
          selectedData.testType,
        ],
        queryFn: () =>
          selectedData.testType
            ? TESTS_DETAILS.GET_SUPPORTED_LANGUAGES_FOR_TEST_TYPE(
                selectedData.testType
              )
            : [],
        staleTime: 1000 * 60 * 60,
        enabled:
          Boolean(selectedData.testType) && Boolean(permissionData?.result),
      },
    ],
  });

  const TEST_TYPES =
    allTests?.map((test) => ({
      label: PRODUCTS[test.type].typeName,
      value: test.type,
    })) || [];

  const subTestsList = queries[0]?.data || [];
  const areSubTestsLoading = queries[0]?.isLoading;
  const areSubTestsFetching = queries[0]?.isFetching;

  const subTestsLanguagesList = queries[1]?.data || [];
  const areSupportedTestLanguagesLoading = queries[1]?.isLoading;
  const areSupportedTestLanguagesFetching = queries[1]?.isFetching;

  // TODO FIX SUBTEST TYPE
  const subTests =
    // eslint-disable-next-line @typescript-eslint/no-explicit-any
    subTestsList?.map((test: any) => ({
      label: test.name,
      value: test.order,
    })) || [];

  const testTypeSupportedLanguages =
    subTestsLanguagesList?.map((lang: string) => ({
      label: getLangNameFromCode(lang)?.native || '',
      value: lang,
    })) || [];

  const areParticipantsSelected = selectedData.participants.length > 0;

  const isInvalidToConductTest =
    !areParticipantsSelected ||
    !selectedData.language ||
    !selectedData.testLevel ||
    !selectedData.selectedDevice;

  // const isConductButtonDisabled =
  //   selectedData.selectedDevice === 'student' || isInvalidToConductTest;

  // const isConductButtonDisabled = isInvalidToConductTest;

  // const isScheduleButtonDisabled =
  //   selectedData.selectedDevice === 'teacher' || isInvalidToConductTest;

  const isStudyExpired =
    permissionData?.cause ===
    'CreateSessionExceptions.CannotCreateSessionStudyExpired';

  // const isStudyNotFound =
  //   permissionData?.cause ===
  //   'CreateSessionExceptions.CannotCreateSessionNoStudy';

  return (
    <div className={s.container}>
      <div className={s.header}>
        <div className={s.textWrapper}>
          <Text transKey="create-test" type="h3" />
        </div>

        <CloseButton onClick={onCloseModal} variant="outlined" />
      </div>

      {(permissionData?.result || isPermissionLoading) && (
        <>
          <div className={s.radioGroup}>
            {!isResearcher && (
              <div
                className={s.radioWrapper}
                onClick={() => {
                  handleSelectedDataUpdate('selectedDevice', 'teacher');

                  if (selectedData.participants.length > 1) {
                    handleSelectedDataUpdate('participants', []);
                  }
                }}
              >
                <Radio
                  value="teacher"
                  isChecked={selectedData.selectedDevice === 'teacher'}
                />

                <FaLaptop
                  size={20}
                  color={
                    selectedData.selectedDevice === 'teacher'
                      ? 'var(--color-black)'
                      : 'var(--color-gray)'
                  }
                />

                <Text
                  transKey="this-device"
                  type="body1"
                  isBold={selectedData.selectedDevice === 'teacher'}
                />
              </div>
            )}

            <div
              className={s.radioWrapper}
              onClick={() => {
                handleSelectedDataUpdate('selectedDevice', 'student');
              }}
            >
              <Radio
                value="student"
                isChecked={selectedData.selectedDevice === 'student'}
              />

              <IoPeople
                size={20}
                color={
                  selectedData.selectedDevice === 'student'
                    ? 'var(--color-black)'
                    : 'var(--color-gray)'
                }
              />

              <Text
                transKey="students-device"
                type="body1"
                isBold={selectedData.selectedDevice === 'student'}
              />
            </div>
          </div>

          <div className={s.content}>
            <Card size="xl" overflow="visible">
              <div className={s.row}>
                <div className={s.row}>
                  <Icon
                    name="TestSvg"
                    color="turquoise"
                    size="lg"
                    className={s.icon}
                  />

                  <Text transKey="select-test-and-language" type="h4" />
                </div>

                <div className={s.dropDownsWrapper}>
                  <SelectDropdown
                    value={selectedData.testType || ''}
                    data={TEST_TYPES}
                    placeholder={t('test-type')}
                    onChange={(value) =>
                      handleSelectedDataUpdate('testType', value)
                    }
                    isDisabled={areTestsLoading}
                  />

                  <SelectDropdown
                    value={selectedData.language || ''}
                    data={testTypeSupportedLanguages}
                    placeholder={`${t('language')} *`}
                    onChange={(value) => {
                      handleSelectedDataUpdate('language', value);

                      if (value !== selectedData.language) {
                        handleSelectedDataUpdate('subTests', []);
                      }
                    }}
                    isDisabled={
                      !selectedData.testType ||
                      areSupportedTestLanguagesFetching ||
                      areSubTestsFetching
                    }
                  />

                  <SelectDropdown
                    value={selectedData.subTests}
                    data={subTests}
                    placeholder={t('all-subtests')}
                    onChange={(value) =>
                      handleSelectedDataUpdate(
                        'subTests',
                        selectedData.subTests.includes(value)
                          ? selectedData.subTests.filter(
                              (subTest) => subTest !== value
                            )
                          : [...selectedData.subTests, value]
                      )
                    }
                    type="multi-select"
                    allValuesSelectedText={t('all-subtests')}
                    onSelectAllToggle={(value) =>
                      handleSelectedDataUpdate('subTests', value)
                    }
                    isDisabled={
                      !selectedData.testType ||
                      areSupportedTestLanguagesFetching ||
                      areSubTestsFetching ||
                      subTestsList.length < 1 ||
                      !isUserAdminOrDeveloper
                    }
                  />
                </div>
              </div>

              <Divider
                mb={30}
                mt={30}
                variant="dashed"
                style={{ borderColor: 'var(--color-gray)' }}
              />

              <div className={s.row}>
                <div className={s.row}>
                  <Icon
                    name="InProgressSvg"
                    color="turquoise"
                    size="lg"
                    className={s.icon}
                  />

                  <Text transKey="select-tests-grade" type="h4" />
                </div>

                <CircleButtonSelector
                  options={SUPPORTED_SCHOOL_GRADES}
                  onSelectOption={(level) => {
                    handleSelectedDataUpdate('testLevel', level);
                    handleSelectedDataUpdate('subTests', []);
                  }}
                  selectedOption={selectedData.testLevel}
                />
              </div>
            </Card>

            <Card size="xl">
              <div className={s.row}>
                <div className={s.row}>
                  <Icon
                    name="StudentSvg"
                    color="turquoise"
                    size="lg"
                    className={s.icon}
                  />

                  <Text transKey="selected-students" type="h4" />

                  <Icon name="CheckMarkSvg" color="turquoise" size="md" />

                  <Text
                    untranslatedText={`${selectedData?.participants.length}`}
                    color="turquoise"
                    type="body1"
                  />
                </div>

                <Button
                  transKey={
                    areParticipantsSelected
                      ? 'add-remove-capital'
                      : 'select-capital'
                  }
                  onClick={onSelectParticipantsClick}
                  variant="primary"
                />
              </div>

              <ScrollArea.Autosize
                mah={160}
                type="always"
                scrollbarSize={4}
                mt={32}
              >
                {areParticipantsSelected ? (
                  <div className={s.selectedParticipants}>
                    {selectedData.participants.map((participant) => (
                      <DisplayCloseTag
                        key={participant.id}
                        text={participant.displayedName}
                      />
                    ))}
                  </div>
                ) : (
                  <Text
                    transKey="no-participants-selected"
                    type="body2"
                    align="center"
                    color="blue"
                    mt={32}
                  />
                )}
              </ScrollArea.Autosize>
            </Card>
          </div>

          <div className={s.footer}>
            <div className={s.buttonsWrapper}>
              {/* // Enable when schedule feature is wanted */}
              {/* <Button
            transKey="schedule-capital"
            variant="primary"
            onClick={onScheduleButtonClick}
            isDisabled={isScheduleButtonDisabled}
          /> */}
              <Button
                transKey="assign-capital"
                variant="primary"
                onClick={onConductButtonClick}
                isDisabled={isInvalidToConductTest}
                isLoading={isActionButtonLoading}
              />
            </div>
          </div>
        </>
      )}

      {!permissionData?.result && !isPermissionLoading && (
        <Flex justify="center" align="center" direction="column">
          <Text
            transKey={
              isStudyExpired
                ? 'create-test-session-study-expired-description'
                : 'create-test-session-study-not-found-description'
            }
            type="h3"
            align="center"
            color="blue"
            mt={32}
            mb={64}
          />

          <Button transKey="close-capital" onClick={onCloseModal} />
        </Flex>
      )}

      <LoadingOverlay
        visible={
          areSubTestsLoading ||
          areSupportedTestLanguagesLoading ||
          isPermissionLoading
        }
        overlayProps={{ blur: 1 }}
      />
    </div>
  );
};

export default SelectTestScreen;
