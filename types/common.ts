import { z } from 'zod';

import {
  CURRENCY_SYMBOLS,
  HTTP_STATUS_CODES,
  PRODUCTS,
  SUPPORTED_GENDERS,
  SUPPORTED_SCHOOL_GRADES,
} from '@/common/consts';
import { APP_LAYOUT_ROUTES } from '@/common/routes';
import { Icons } from '@/components/Icon/Icons';
import baseTheme from '@/styles/baseTheme';
import en from '@/translations/en.json';
import {
  ADMIN_SCHOOLS_LIST_SCHEMA,
  SCHOOLS_SCHEMA,
} from '@/zod/responseSchemas/admin/schools';
import {
  ADMIN_STUDIES_LIST_SCHEMA,
  ADMIN_STUDY_SCHEMA__DETAILS_BY_ID,
  ADMIN_STUDY_TESTS_SCHEMA,
  ADMIN_TESTS_LIST_SCHEMA,
  STUDIES_SCHEMA,
} from '@/zod/responseSchemas/admin/studies';
import {
  ADMIN_USER_SCHEMA,
  UNASSIGNED_RESEARCHERS_SCHEMA,
  UNASSIGNED_STUDY_ADMINS_SCHEMA,
  USERS_SCHEMA_ADMIN,
} from '@/zod/responseSchemas/admin/users';
import { GET_STUDENT_LOGIN_WELCOME_DETAILS_SCHEMA } from '@/zod/responseSchemas/auth';
import {
  CERTIFICATE_PROGRESS_MODULE_ANSWER_SCHEMA,
  CERTIFICATE_PROGRESS_SCHEMA_BY_TYPE,
  MODULE_PROGRESS_SCHEMA_BY_CERTIFICATE_TYPE_AND_ORDER,
} from '@/zod/responseSchemas/certificate-progress';
import {
  GET_TEMPLATE_URL_SCHEMA,
  GET_UPLOAD_PROGRESS_SCHEMA,
  GET_UPLOAD_URL_SCHEMA,
} from '@/zod/responseSchemas/data';
import {
  CERTIFICATIONS_PRODUCTS_SCHEMA,
  LICENSES_PRODUCT_SCHEMA,
  PRODUCT_DETAILS_SCHEMA,
} from '@/zod/responseSchemas/products';
import {
  PURCHASES_HISTORY_SCHEMA,
  VAT_COUNTRIES_LIST_SCHEMA,
} from '@/zod/responseSchemas/purchases';
import {
  TEST_ERROR_RESULTS_SCHEMA,
  TEST_SCORES_RESULT_SCHEMA,
} from '@/zod/responseSchemas/results';
import {
  ADD_CLASS_RESPONSE_SCHEMA,
  CLASS_DETAILS_SCHEMA,
  GET_CLASSES_SCHEMA,
} from '@/zod/responseSchemas/schools/classes';
import {
  GET_TEACHERS_SCHEMA,
  TEACHER_SCHEMA,
  TEACHERS_DETAILS_SCHEMA,
} from '@/zod/responseSchemas/schools/teachers';
import {
  ADD_GROUP_RESPONSE_SCHEMA,
  GET_GROUPS_SCHEMA,
  GROUP_DETAILS_SCHEMA,
} from '@/zod/responseSchemas/students/groups';
import { STUDIES_LIST_SCHEMA } from '@/zod/responseSchemas/studies';
import { ALL_APPLICATION_TESTS_SCHEMA } from '@/zod/responseSchemas/tests/details';
import {
  GET_ADJUST_AUDIO_DETAILS_SCHEMA,
  GET_CURRENT_EXERCISE_FROM_TEST_SESSION_BY_ID_SCHEMA,
  GET_INTRO_SCHEMA_FOR_CURRENT_SUB_TEST_SCHEMA,
  GET_PROGRESS_FROM_TEST_SESSION_BY_ID_SCHEMA,
  GET_STUDENT_WELCOME_DETAILS_SCHEMA,
  GET_TEST_LOCALES_SCHEMA,
} from '@/zod/responseSchemas/tests/sessions-action';
import { SKIP_TEST_SCHEMA } from '@/zod/responseSchemas/tests/sessions-admin';
import { CAN_CREATE_TEST_SESSION_SCHEMA } from '@/zod/responseSchemas/tests/sessions-create';
import {
  TEST_SESSION_EXPORT_SCHEMA,
  TEST_SESSIONS_LIST_SCHEMA,
} from '@/zod/responseSchemas/tests/sessions-listing.';
import { USER_DETAILS_SCHEMA } from '@/zod/responseSchemas/users';
import {
  ADMIN_STUDY_SCHEMA,
  ADMIN_TEST_SCHEMA,
  ARBITRARY_SKILL_SCHEMA,
  STUDENT_SCHEMA,
} from '@/zod/zodConstants';
import {
  ACCEPT_INVITATION_FORM_SCHEMA,
  ADD_CLASS_FORM_SCHEMA,
  ADD_GROUP_FORM_SCHEMA,
  ADD_PROFILE_INVOICE_BILLING_FORM_SCHEMA,
  ADD_PROFILE_RECEIPT_BILLING_FORM_SCHEMA,
  COMPLETE_USER_REGISTRATION_FORM_SCHEMA,
  DEFAULT_CHECKOUT_FORM_SCHEMA,
  LOGIN_USER_FORM_SCHEMA,
  PROFILE_FORM_SCHEMA,
  RESET_USER_PASSWORD_FORM_SCHEMA,
  SIGNUP_USER_FORM_SCHEMA,
} from '@/zod/zodFormValidationSchemas';

const { borderRadius, colors, spacing } = baseTheme;

// AUTH
export type LoginUserFormValuesType = z.infer<typeof LOGIN_USER_FORM_SCHEMA>;

export type ResetUserPasswordFormValuesType = z.infer<
  typeof RESET_USER_PASSWORD_FORM_SCHEMA
>;

export type CompleteUserRegistrationFormValuesType = z.infer<
  typeof COMPLETE_USER_REGISTRATION_FORM_SCHEMA
>;
export type AcceptInvitationFormValuesType = z.infer<
  typeof ACCEPT_INVITATION_FORM_SCHEMA
>;

// PRODUCTS
export type ProductDetailsType = z.infer<typeof PRODUCT_DETAILS_SCHEMA>;
export type FirstTimePurchaseFormType = z.infer<
  typeof DEFAULT_CHECKOUT_FORM_SCHEMA
>;

export type SignUpFormSchema = z.infer<typeof SIGNUP_USER_FORM_SCHEMA>;

export type CertificationsProductsType = z.infer<
  typeof CERTIFICATIONS_PRODUCTS_SCHEMA
>;

export type LicensesProductsType = z.infer<typeof LICENSES_PRODUCT_SCHEMA>;

// USERS
export type UserDetailsType = z.infer<typeof USER_DETAILS_SCHEMA>;

// PROFILES
export type ProfileFormValuesType = z.infer<typeof PROFILE_FORM_SCHEMA>;

export type AddProfileAddressInvoiceFormType = z.infer<
  typeof ADD_PROFILE_INVOICE_BILLING_FORM_SCHEMA
>;

export type AddProfileAddressReceiptFormType = z.infer<
  typeof ADD_PROFILE_RECEIPT_BILLING_FORM_SCHEMA
>;

// TESTS
export type AllApplicationTestsDetailsType = z.infer<
  typeof ALL_APPLICATION_TESTS_SCHEMA
>;

export type TestSessionsListType = z.infer<typeof TEST_SESSIONS_LIST_SCHEMA>;

export type TestSessionsListResponseType = z.infer<
  typeof TEST_SESSIONS_LIST_SCHEMA
>['results'];

export type TestSessionsExportType = z.infer<typeof TEST_SESSION_EXPORT_SCHEMA>;

export type SubTestIntroType = z.infer<
  typeof GET_INTRO_SCHEMA_FOR_CURRENT_SUB_TEST_SCHEMA
>;

export type AdjustAudioDetailsType = z.infer<
  typeof GET_ADJUST_AUDIO_DETAILS_SCHEMA
>;

export type TestLocalesType = z.infer<typeof GET_TEST_LOCALES_SCHEMA>;

export type StudentWelcomeDetailsType = z.infer<
  typeof GET_STUDENT_WELCOME_DETAILS_SCHEMA
>;

export type StudentWelcomeLoginDetailsType = z.infer<
  typeof GET_STUDENT_LOGIN_WELCOME_DETAILS_SCHEMA
>;

// test session id stats
export type ConductedTestSessionStatsType = z.infer<
  typeof GET_PROGRESS_FROM_TEST_SESSION_BY_ID_SCHEMA
>;

export type SubTestQuestionType = z.infer<
  typeof GET_CURRENT_EXERCISE_FROM_TEST_SESSION_BY_ID_SCHEMA
>;

// Test admin action
export type TestAdminActionType = z.infer<typeof SKIP_TEST_SCHEMA>;

// Test results
export type TestScoresResultsType = z.infer<typeof TEST_SCORES_RESULT_SCHEMA>;
export type TestErrorsResultsType = z.infer<typeof TEST_ERROR_RESULTS_SCHEMA>;

export type CanCreateSessionTestType = z.infer<
  typeof CAN_CREATE_TEST_SESSION_SCHEMA
>;

// CERTIFICATE PROGRESS
export type CertificateProgressType = z.infer<
  typeof CERTIFICATE_PROGRESS_SCHEMA_BY_TYPE
>;
export type CertificateProgressModuleAnswerType = z.infer<
  typeof CERTIFICATE_PROGRESS_MODULE_ANSWER_SCHEMA
>;
export type ModuleProgressSchemaByCertificateAndOrderType = z.infer<
  typeof MODULE_PROGRESS_SCHEMA_BY_CERTIFICATE_TYPE_AND_ORDER
>;

// SCHOOLS - TEACHERS
export type TeachersListType = z.infer<typeof GET_TEACHERS_SCHEMA>;
export type TeacherDetailsType = z.infer<typeof TEACHER_SCHEMA>;
export type TeachersDetailsType = z.infer<typeof TEACHERS_DETAILS_SCHEMA>;
export type InvitationStatusType = TeacherDetailsType['invitation']['state'];
export type TeacherStatusType = TeacherDetailsType['status'];

// A UNIFIED STUDENT TYPE THAT REFERS TO BOTH STUDENTS FOR SCHOOLS AND INDEPENDENT TEACHERS
export type StudentType = z.infer<typeof STUDENT_SCHEMA>;

// THIS IS USED FOR EVERY PAGINATED FETCHING OF STUDENTS
export type PaginatedStudentList = {
  limit: number;
  page: number;
  count: number;
  totalCount: number;
  results: StudentType[];
};

// SCHOOLS - CLASSES
export type AddClassFormType = z.infer<typeof ADD_CLASS_FORM_SCHEMA>;
export type AddClassResponseType = z.infer<typeof ADD_CLASS_RESPONSE_SCHEMA>;
export type ClassesFetchListType = z.infer<typeof GET_CLASSES_SCHEMA>;

export type SchoolsListType = z.infer<typeof SCHOOLS_SCHEMA>;

// RENAME THIS TO CLASS_BY_ID_TYPE
// TODO : This type is specific when getting a class by id and it will include students as well
export type ClassDetailsType = z.infer<typeof CLASS_DETAILS_SCHEMA>;

// This is a single example of a class from the list of all classes
// TODO :  RENAME THIS TO ClassDetailsType
export type SingleClassDetailsType = ClassesFetchListType['results'][0];

// STUDENTS - GROUPS
export type GroupsFetchListType = z.infer<typeof GET_GROUPS_SCHEMA>;
// RENAME THIS TO GROUP_BY_ID_TYPE
export type GroupDetailsType = z.infer<typeof GROUP_DETAILS_SCHEMA>;

// This is a single example of a GROUP from the list of all fetched groups
// TODO :  RENAME THIS TO GroupDetailsType
export type SingleGroupDetailsType = GroupsFetchListType['results'][0];

export type AddGroupFormType = z.infer<typeof ADD_GROUP_FORM_SCHEMA>;
export type AddGroupResponseType = z.infer<typeof ADD_GROUP_RESPONSE_SCHEMA>;

// PURCHASES
export type PurchasesHistoryType = z.infer<typeof PURCHASES_HISTORY_SCHEMA>;

export type VatCountriesListType = z.infer<typeof VAT_COUNTRIES_LIST_SCHEMA>;

// STUDENTS / SKILLS
export type ArbitrarySkillType = z.infer<typeof ARBITRARY_SKILL_SCHEMA>;

// DATA
export type DownloadTemplateType = z.infer<typeof GET_TEMPLATE_URL_SCHEMA>;

export type UploadFileProgressType = z.infer<typeof GET_UPLOAD_PROGRESS_SCHEMA>;

export type PresignedUrlResponseType = z.infer<typeof GET_UPLOAD_URL_SCHEMA>;

// STUDIES
export type StudiesListType = z.infer<typeof STUDIES_LIST_SCHEMA>;
export type AdminStudiesListType = z.infer<typeof STUDIES_SCHEMA>;

// ADMIN USERS

export type UnassignedStudyAdminsPaginatedResponseType = z.infer<
  typeof UNASSIGNED_STUDY_ADMINS_SCHEMA
>;

export type UsersAdmin = z.infer<typeof USERS_SCHEMA_ADMIN>;

export type AdminUserType = z.infer<typeof ADMIN_USER_SCHEMA>;

export type UnassignedResearchersPaginatedResponseType = z.infer<
  typeof UNASSIGNED_RESEARCHERS_SCHEMA
>;

export type UnassignedResearchersType =
  UnassignedResearchersPaginatedResponseType['results'][number];

// ADMIN SCHOOLS

export type AdminSchoolsListType = z.infer<typeof ADMIN_SCHOOLS_LIST_SCHEMA>;

// ADMIN STUDIES

export type StudyDetailsType = {
  name: string;
  type: string | null;
  country: string;
  language: string;
  administrator: {
    id: string;
    firstName: string;
    lastName: string;
  } | null;
  from: Date | null;
  to: Date | null;
  researchers: UnassignedResearchersType[];
};

export type AdminStudyType = z.infer<typeof ADMIN_STUDY_SCHEMA>;

export type AdminStudyDetailsByIdType = z.infer<
  typeof ADMIN_STUDY_SCHEMA__DETAILS_BY_ID
>;

export type AdminStudiesPaginatedResponseType = z.infer<
  typeof ADMIN_STUDIES_LIST_SCHEMA
>;

export type AdminTestsPaginatedResponseType = z.infer<
  typeof ADMIN_TESTS_LIST_SCHEMA
>;

export type AdminStudiesTestsType = z.infer<typeof ADMIN_STUDY_TESTS_SCHEMA>;

// -------------------------------------- ---------------------------------------- --------------------------------

// THEMING TYPES
// -------------------------------------- ---------------------------------------- --------------------------------

export type ColorsType = keyof typeof colors;
export type SpacingType = keyof typeof spacing;
export type BorderRadiusType = keyof typeof borderRadius;
export type ButtonVariantsType =
  | 'primary'
  | 'secondary'
  | 'destructive'
  | 'success'
  | 'dark'
  | 'danger'
  | 'primaryOutlined'
  | 'dangerOutlined'
  | 'text'
  | 'test'
  | 'turquoisePrimary';

// -------------------------------------- ---------------------------------------- --------------------------------

export type CloseButtonVariantsType = 'outlined' | 'primary';

export type SUPPORTED_HTTP_STATUS_CODES =
  (typeof HTTP_STATUS_CODES)[keyof typeof HTTP_STATUS_CODES];

export type TranslationKeysType = keyof typeof en;

export type ProductsType = keyof typeof PRODUCTS;

export type CurrencySymbolType = keyof typeof CURRENCY_SYMBOLS;

export type IconType = keyof typeof Icons;

export type DashboardRoutesType =
  (typeof APP_LAYOUT_ROUTES)[keyof typeof APP_LAYOUT_ROUTES];

type Enumerate<
  N extends number,
  Acc extends number[] = [],
> = Acc['length'] extends N
  ? Acc[number]
  : Enumerate<N, [...Acc, Acc['length']]>;

export type Range<F extends number, T extends number> = Exclude<
  Enumerate<T>,
  Enumerate<F>
>;

export type Nullable<T> = {
  [K in keyof T]?: T[K];
};

export type DeepNullable<T> = {
  [K in keyof T]?: DeepNullable<T[K]>;
};

export type Optional<T> = {
  [K in keyof T]?: T[K];
};

export type NonUndefined<T> = T extends undefined ? never : T;

export type DeepOptional<T> = {
  [K in keyof T]?: DeepOptional<T[K]>;
};

export type Writeable<T> = { -readonly [P in keyof T]: T[P] };

export type DeepWriteable<T> = {
  -readonly [P in keyof T]: DeepWriteable<T[P]>;
};

export type UserCertificationsType = {
  [key: string]: {
    type: ProductsType;
    version: number;
    isCompleted: boolean;
    isNotBought: boolean;
    isInProgress: boolean;
  };
};

export type SupportedSchoolGradesType =
  (typeof SUPPORTED_SCHOOL_GRADES)[number];

export type SupportedGendersType = (typeof SUPPORTED_GENDERS)[number];

export type BasePaginationType = {
  page: number;
  limit: number;
  query: string;
};

// Its being used under Claim your and its respective components and also Under conduct test students selection and under Admin panel studies Tab
export type SelectedEntityByIdAndDisplayedNameType = {
  displayedName: string;
  id: string;
};
