import { zodResolver } from '@hookform/resolvers/zod';
import { TextInput } from '@mantine/core';
import { notifications } from '@mantine/notifications';
import { useMutation } from '@tanstack/react-query';
import { useRouter } from 'next/router';
import React, { useState } from 'react';
import { Controller, useForm } from 'react-hook-form';
import { useTranslation } from 'react-i18next';

import { PASSWORD_REQUIREMENTS, SUPPORTED_LANGUAGES } from '@/common/consts';
import { PUBLIC_ROUTES } from '@/common/routes';
import Button from '@/components/Button/Button';
import Checkbox from '@/components/Checkbox/Checkbox';
import PasscodeInput from '@/components/PasscodeInput/PasscodeInput';
import SelectDropdown from '@/components/SelectDropdown/SelectDropdown';
import Text from '@/components/Text/Text';
import AUTH from '@/services/auth';
import { SignUpFormSchema } from '@/types/common';
import { SIGNUP_USER_FORM_SCHEMA } from '@/zod/zodFormValidationSchemas';

import { DEFAULT_SIGN_UP_FORM_VALUES } from '../../consts';
import { ModalContentType } from '../../types';
import TermsAndPrivacyModal from '../TermsAndPrivacyModal/TermsAndPrivacyModal';
import styles from './SignUpForm.module.css';

type SignUpFormProps = {
  handleStepUpdate: (step: number) => void;
  cacheUserEmail: (email: string) => void;
  cacheUserUsername: (username: string) => void;
};

const SignUpForm = ({
  cacheUserEmail,
  cacheUserUsername,
  handleStepUpdate,
}: SignUpFormProps) => {
  const { i18n, t } = useTranslation();
  const router = useRouter();
  const currentLanguage = i18n.language;

  const [modalContentType, setModalContentType] =
    useState<ModalContentType>('privacy');
  const [isModalOpen, setIsModalOpen] = useState<boolean>(false);
  const [checkboxValue, setCheckboxValue] = useState<string>('');

  const {
    control,
    formState: { errors },
    handleSubmit,
    register,
    watch,
  } = useForm<SignUpFormSchema>({
    resolver: zodResolver(SIGNUP_USER_FORM_SCHEMA),
    defaultValues: {
      ...DEFAULT_SIGN_UP_FORM_VALUES,
      language: currentLanguage,
    },
    mode: 'onSubmit',
  });

  const handleModalAction = (type: ModalContentType) => {
    setModalContentType(type);
    setIsModalOpen(true);
  };

  const hasUserAgreedToTerms = checkboxValue === 'agreed';

  const signUpMutation = useMutation({
    mutationFn: AUTH.REGISTER,
    onSuccess: () => {
      handleStepUpdate(2);
    },
    onError: (error) => {
      if (error.message === 'AuthError.UserAlreadyHasAccount') {
        handleStepUpdate(3);
      } else {
        notifications.show({
          title: t('error'),
          message: t(error.message),
          color: 'red',
        });
      }
    },
  });

  const isSubmitButtonDisabled = watch('password') !== watch('confirmPassword');

  const onSubmitForm = (data: SignUpFormSchema) => {
    cacheUserEmail(data.email);
    cacheUserUsername(`${data.firstName} ${data.lastName}`);

    signUpMutation.mutate(data);
  };

  return (
    <>
      <form onSubmit={handleSubmit(onSubmitForm)}>
        <div className={styles.row}>
          <TextInput
            {...register('firstName', {
              required: t('required'),
            })}
            placeholder={t('firstname')}
            variant="primary"
            error={t(errors.firstName?.message || '')}
            w="50%"
          />

          <TextInput
            {...register('lastName', {
              required: t('required'),
            })}
            placeholder={t('lastname')}
            variant="primary"
            error={t(errors.lastName?.message || '')}
            w="50%"
          />
        </div>

        <div className={styles.row}>
          <div className={styles.fullWidth}>
            <TextInput
              {...register('email', {
                required: t('required'),
              })}
              placeholder={t('email')}
              variant="primary"
              error={t(errors.email?.message || '')}
            />
          </div>

          <Controller
            name="language"
            control={control}
            rules={{ required: t('required') }}
            render={({ field }) => (
              <SelectDropdown
                value={field.value || currentLanguage}
                data={Array.from(
                  Object.entries(SUPPORTED_LANGUAGES),
                  ([key, value]) => ({
                    label: value.name,
                    value: value.shortCode,
                  })
                )}
                onChange={(v) => {
                  i18n.changeLanguage(v);

                  field.onChange(v);
                }}
                placeholder={t('language')}
              />
            )}
          />
        </div>

        <div className={styles.row}>
          <div className={styles.fullWidth}>
            <Controller
              name="password"
              control={control}
              rules={{ required: t('required') }}
              render={({ field }) => (
                <PasscodeInput
                  placeholderText={t('password')}
                  value={field.value}
                  onChange={(v) => {
                    field.onChange(v);
                  }}
                  requirements={PASSWORD_REQUIREMENTS}
                  error={t(errors.password?.message || '')}
                />
              )}
            />
          </div>

          <div className={styles.fullWidth}>
            <Controller
              name="confirmPassword"
              control={control}
              rules={{ required: t('required') }}
              render={({ field }) => (
                <PasscodeInput
                  placeholderText={t('confirm-password')}
                  value={field.value}
                  type="confirm-password"
                  onChange={(v) => {
                    field.onChange(v);
                  }}
                  requirements={[
                    {
                      re: new RegExp(
                        `^${watch('password')?.replace(/[-/\\^$*+?.()|[\]{}]/g, '\\$&') || '#'}$`
                      ),
                      label: 'matches-password',
                    },
                  ]}
                  error={t(errors.confirmPassword?.message || '')}
                />
              )}
            />
          </div>
        </div>

        <div className={styles.termsWrapper}>
          <Checkbox
            value="agreed"
            onChange={(value) => setCheckboxValue(value)}
            isChecked={hasUserAgreedToTerms}
            variant="outlined"
            isRequired
          />

          <div className={styles.termsDescription}>
            <Text
              transKey="i-have-read-and-agreed"
              type="body2"
              color="black"
              fw={300}
            />
            <Text
              transKey="terms-of-service"
              type="body2"
              color="blue"
              fw={700}
              onClick={() => handleModalAction('terms')}
            />
            <Text transKey="and" type="body2" color="black" fw={300} />
            <Text
              transKey="privacy-policy"
              type="body2"
              color="blue"
              fw={700}
              onClick={() => handleModalAction('privacy')}
            />
            <Text transKey="pages" type="body2" color="black" fw={300} />
          </div>
        </div>

        <div className={styles.signInWrapper}>
          <Text
            untranslatedText={t('already-have-an-account')}
            type="body2"
            color="black"
            fw={300}
          />

          <Text
            untranslatedText={t('login')}
            type="body2"
            color="blue"
            fw={700}
            onClick={() => {
              router.push(PUBLIC_ROUTES.LOGIN);
            }}
          />
        </div>

        <Button
          transKey="sign-up-capital"
          hasFullWidth
          type="submit"
          isLoading={signUpMutation.isPending}
          isDisabled={isSubmitButtonDisabled}
        />
      </form>

      <TermsAndPrivacyModal
        isOpen={isModalOpen}
        onClose={() => {
          setIsModalOpen(false);
        }}
        type={modalContentType}
      />
    </>
  );
};

export default SignUpForm;
