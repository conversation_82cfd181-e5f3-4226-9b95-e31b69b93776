/* eslint-disable jsx-a11y/no-static-element-interactions */
/* eslint-disable jsx-a11y/click-events-have-key-events */
import { zodResolver } from '@hookform/resolvers/zod';
import { TextInput } from '@mantine/core';
import { notifications } from '@mantine/notifications';
import { useMutation, useQuery } from '@tanstack/react-query';
import { useRouter } from 'next/router';
import React, { useState } from 'react';
import { Controller, useForm } from 'react-hook-form';
import { useTranslation } from 'react-i18next';

import { USERS_ERRORS } from '@/common/errors';
import QUERY_KEYS from '@/common/queryKeys';
import Button from '@/components/Button/Button';
import Radio from '@/components/Radio/Radio';
import SelectDropdown from '@/components/SelectDropdown/SelectDropdown';
import Text from '@/components/Text/Text';
import PROFILES from '@/services/profiles';
import PURCHASES from '@/services/purchases';
import {
  AddProfileAddressInvoiceFormType,
  VatCountriesListType,
} from '@/types/common';
import {
  ADD_PROFILE_INVOICE_BILLING_FORM_SCHEMA,
  ADD_PROFILE_RECEIPT_BILLING_FORM_SCHEMA,
} from '@/zod/zodFormValidationSchemas';

import styles from './BillingForm.module.css';

type BillingFormProps = {
  handleStepUpdate: (step: number) => void;
  productId: string;
};

const BillingForm = ({ handleStepUpdate, productId }: BillingFormProps) => {
  const { t } = useTranslation();
  const router = useRouter();

  const [paymentMethod, setPaymentMethod] = useState<'receipt' | 'invoice'>(
    'receipt'
  );

  const [isInvalidVat, setIsInvalidVat] = useState(false);

  const {
    clearErrors,
    control,
    formState: { errors },
    handleSubmit,
    register,
  } = useForm<AddProfileAddressInvoiceFormType>({
    resolver: zodResolver(
      paymentMethod === 'receipt'
        ? ADD_PROFILE_RECEIPT_BILLING_FORM_SCHEMA
        : ADD_PROFILE_INVOICE_BILLING_FORM_SCHEMA
    ),
    mode: 'onSubmit',
  });

  const updateUserMutation = useMutation({
    mutationFn: PROFILES.UPDATE_USER_PROFILE,
    onSuccess: () => {
      handleStepUpdate(4);
    },
    onError: (error) => {
      if (error.message === USERS_ERRORS.INVALID_TAX_ID) {
        setIsInvalidVat(true);
      } else {
        notifications.show({
          title: t('error'),
          message: t(error.message),
          color: 'red',
        });
      }
    },
  });

  const { data: vatCountriesList, isLoading: isLoadingVatCountriesList } =
    useQuery<VatCountriesListType | null | undefined>({
      enabled: router.isReady && Boolean(productId),
      queryFn: PURCHASES.GET_VAT_COUNTRIES_LIST,
      queryKey: [QUERY_KEYS.VAT_COUNTRIES_LIST, productId],
    });

  const onSubmitForm = (data: AddProfileAddressInvoiceFormType) => {
    updateUserMutation.mutate({
      vatNumber: paymentMethod === 'invoice' ? data.vatNumber : null,
      address: {
        addressLine1: data.address?.addressLine1 || '',
        city: data.address?.city || '',
        postcode: data.address?.postcode || '',
        country: data.address?.country || '',
      },
    });
  };

  const clearVatNumberError = () => {
    if (isInvalidVat) {
      setIsInvalidVat(false);
    }

    clearErrors('vatNumber');
  };

  const handleRadioButtonClick = (action: 'receipt' | 'invoice') => {
    setPaymentMethod(action);
    clearErrors('vatNumber');
  };

  return (
    <div>
      <div className={styles.radioContainer}>
        <div
          className={styles.radioWrapper}
          onClick={() => {
            handleRadioButtonClick('receipt');
          }}
        >
          <Radio value="receipt" isChecked={paymentMethod === 'receipt'} />

          <Text transKey="individual" type="subTitle2" />
        </div>

        <div
          className={styles.radioWrapper}
          onClick={() => {
            handleRadioButtonClick('invoice');
          }}
        >
          <Radio value="invoice" isChecked={paymentMethod === 'invoice'} />

          <Text transKey="company" type="subTitle2" />
        </div>
      </div>

      <form onSubmit={handleSubmit(onSubmitForm)} className={styles.form}>
        <TextInput
          placeholder={t('address')}
          error={t(errors.address?.addressLine1?.message || '')}
          {...register('address.addressLine1')}
        />

        <div className={styles.row}>
          <TextInput
            placeholder={t('city')}
            {...register('address.city')}
            error={t(errors.address?.city?.message || '')}
            className={styles.smallInput}
          />

          <TextInput
            placeholder={t('post-code')}
            {...register('address.postcode')}
            error={t(errors.address?.postcode?.message || '')}
            className={styles.smallInput}
          />
        </div>

        <div className={styles.row}>
          <Controller
            name="address.country"
            control={control}
            rules={{
              required: paymentMethod === 'invoice' && t('required'),
            }}
            render={({ field }) => (
              <SelectDropdown
                value={field.value || ''}
                isDisabled={isLoadingVatCountriesList}
                data={
                  vatCountriesList?.map((item) => ({
                    label: item.name || '',
                    value: item.code || '',
                  })) || []
                }
                onChange={(v) => {
                  field.onChange(v);
                  clearVatNumberError();
                }}
                placeholder={t('country')}
                clearable
                error={t(errors.address?.country?.message || '')}
              />
            )}
          />

          <TextInput
            placeholder={t('vat-number')}
            {...register('vatNumber')}
            error={
              isInvalidVat
                ? t('invalid-vat-number')
                : t(errors.vatNumber?.message || '')
            }
            onChange={clearVatNumberError}
            disabled={paymentMethod === 'receipt'}
            className={styles.smallInput}
          />
        </div>

        <div className={styles.buttonWrapper}>
          <Button
            transKey="save-capital"
            hasFullWidth
            type="submit"
            isLoading={updateUserMutation.isPending}
          />
        </div>
      </form>
    </div>
  );
};

export default BillingForm;
