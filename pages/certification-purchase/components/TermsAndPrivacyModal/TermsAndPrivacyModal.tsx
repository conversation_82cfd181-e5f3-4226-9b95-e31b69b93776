import { Modal } from '@mantine/core';
import React from 'react';
import { useTranslation } from 'react-i18next';

import Text from '@/components/Text/Text';

import { ModalContentType } from '../../types';
import PRIVACY from './privacy.json';
import TERMS from './terms.json';
import styles from './TermsAndPrivacyModal.module.css';

type TermsAndPrivacyModalProps = {
  type: ModalContentType;
  isOpen: boolean;
  onClose: () => void;
};

const TermsAndPrivacyModal = ({
  isOpen,
  onClose,
  type,
}: TermsAndPrivacyModalProps) => {
  const { i18n } = useTranslation();

  return (
    <Modal
      opened={isOpen}
      onClose={onClose}
      centered
      size="xl"
      autoFocus={false}
      trapFocus
      title={
        <Text
          transKey={type === 'privacy' ? 'privacy-policy' : 'terms-of-service'}
          type="h3"
          color="blue"
          fw={700}
        />
      }
    >
      <div className={styles.contentWrapper}>
        <Text
          untranslatedText={
            type === 'terms'
              ? TERMS[i18n.language as 'en']
              : PRIVACY[i18n.language as 'en']
          }
          type="body1"
          className={styles.content}
        />
      </div>
    </Modal>
  );
};

export default TermsAndPrivacyModal;
