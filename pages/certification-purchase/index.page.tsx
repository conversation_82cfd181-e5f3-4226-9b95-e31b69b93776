/* eslint-disable jsx-a11y/click-events-have-key-events */
/* eslint-disable jsx-a11y/no-static-element-interactions */
import { Skeleton, Stepper } from '@mantine/core';
import { Elements } from '@stripe/react-stripe-js';
import {
  loadStripe,
  StripeElementLocale,
  StripeElementsOptions,
} from '@stripe/stripe-js';
import { keepPreviousData, useQuery } from '@tanstack/react-query';
import { useRouter } from 'next/router';
import { useState } from 'react';
import { useTranslation } from 'react-i18next';

import { appearance } from '@/common/consts';
import { PURCHASES_ERRORS } from '@/common/errors';
import QUERY_KEYS from '@/common/queryKeys';
import Card from '@/components/Card/Card';
import CheckOutCard from '@/components/CertificationCards/CheckOutCard/CheckOutCard';
import BaseLayout from '@/components/Layouts/BaseLayout/BaseLayout';
import Text from '@/components/Text/Text';
import FirstTimePurchaseForm from '@/pages/certification-purchase/components/FirstTimePurchaseForm/FirstTimePurchaseForm';
import FirstTimePurchaseVerification from '@/pages/certification-purchase/components/FirstTimePurchaseVerification/FirstTimePurchaseVerification';
import PRODUCTS from '@/services/products';
import PURCHASES from '@/services/purchases';
import { ProductDetailsType } from '@/types/common';

import styles from './certification-purchase.module.css';
import BillingForm from './components/BillingForm/BillingForm';
import ConfirmForm from './components/ConfirmForm/ConfirmForm';
import FirstTimePurchaseSkeleton from './components/FirstTimePurchaseForm/FirstTimePurchaseSkeleton';
import SignUpForm from './components/SignUpForm/SignUpForm';
import { PaymentStatusType } from './types';

const publicStripeKey = process.env.NEXT_PUBLIC_STRIPE_KEY || '';

const stripePromise = loadStripe(publicStripeKey);

const CertificationPurchase = (): JSX.Element => {
  const [checkoutStatus, setCheckoutStatus] = useState<
    'idle' | PaymentStatusType
  >('idle');

  const [active, setActive] = useState(1);

  const [userEmail, setUserEmail] = useState<string>('');

  const [userUsername, setUserUsername] = useState<string>('');

  const { i18n, t } = useTranslation();

  const router = useRouter();

  const productId = router.query.productId as string;

  const {
    data: clientSecret,
    error: clientSecretError,
    isFetching: isFetchingClientSecret,
  } = useQuery<string | null | undefined>({
    enabled: router.isReady && Boolean(productId) && active === 4,
    queryFn: () =>
      PURCHASES.CREATE_INVOICE({
        products: [productId],
      }),
    queryKey: [QUERY_KEYS.CERTIFICATION_PURCHASE_PAYMENT_INTENT, productId],
  });

  const {
    data: productDetails,
    error: productError,
    isFetching: isFetchingProductDetails,
  } = useQuery<ProductDetailsType | null>({
    enabled: router.isReady && Boolean(productId),
    queryFn: () =>
      PRODUCTS.GET_PRODUCT_DETAILS({
        productId,
      }),
    queryKey: [QUERY_KEYS.PRODUCT_BY_ID, productId],
    placeholderData: keepPreviousData,
  });

  const isProductDetailsLoading =
    isFetchingClientSecret || isFetchingProductDetails;

  const options: StripeElementsOptions = {
    appearance,
    clientSecret: clientSecret || '',
    locale: i18n.language as StripeElementLocale,
    loader: 'never',
  };

  const handlePaymentProcessing = (
    type: PaymentStatusType,
    errorCode?: string
  ) => {
    if (type === 'success') {
      setCheckoutStatus('success');
    } else if (type === 'failure') {
      router.push(
        `/certification-purchase/payment-error?errorType=${errorCode}`
      );
    }
  };

  const getFormContent = () => {
    return (
      <div>
        {isProductDetailsLoading ||
        isFetchingClientSecret ||
        !stripePromise ||
        !clientSecret ||
        !productDetails ? (
          <FirstTimePurchaseSkeleton isReceipt />
        ) : (
          <Elements stripe={stripePromise} options={options}>
            {productDetails && (
              <FirstTimePurchaseForm
                isFetchingPaymentIntent={isProductDetailsLoading}
                clientSecret={clientSecret || ''}
                handlePaymentProcessing={handlePaymentProcessing}
                productDetails={productDetails}
                userEmail={userEmail}
                userUsername={userUsername}
              />
            )}
          </Elements>
        )}
      </div>
    );
  };

  if (
    productError?.message === PURCHASES_ERRORS.PRODUCT_NOT_FOUND ||
    clientSecretError?.message === PURCHASES_ERRORS.PRODUCT_NOT_FOUND
  ) {
    return (
      <Card
        size="2xl"
        radius="xs"
        borderColor="gray100"
        shadow="xs"
        bg="gray100"
        className={styles.wrapper}
      >
        <Text transKey="product-not-found" type="h3" fw={300} />
      </Card>
    );
  }

  return (
    <BaseLayout isAuthPage={false}>
      <div className={styles.wrapper} id="test">
        <Card
          size="2xl"
          radius="xs"
          borderColor="gray100"
          shadow="xs"
          bg="gray100"
        >
          {checkoutStatus === 'idle' && (
            <>
              <Text
                transKey="checkout"
                type="h3"
                mb={15}
                color="black"
                fw={250}
              />

              {!productDetails ? (
                <Skeleton visible height={147} />
              ) : (
                <CheckOutCard productDetails={productDetails} />
              )}

              <Stepper active={active - 1} mt={30}>
                <Stepper.Step label={t('register')}>
                  <SignUpForm
                    handleStepUpdate={(step) => setActive(step)}
                    cacheUserEmail={(email) => setUserEmail(email)}
                    cacheUserUsername={(username) => setUserUsername(username)}
                  />
                </Stepper.Step>

                <Stepper.Step label={t('confirm')}>
                  <ConfirmForm
                    handleStepUpdate={(step) => setActive(step)}
                    userEmail={userEmail}
                  />
                </Stepper.Step>

                <Stepper.Step label={t('billing')}>
                  <BillingForm
                    handleStepUpdate={(step) => setActive(step)}
                    productId={productId}
                  />
                </Stepper.Step>

                <Stepper.Step label={t('payment')}>
                  {getFormContent()}
                </Stepper.Step>
              </Stepper>
            </>
          )}

          {checkoutStatus === 'success' && (
            <FirstTimePurchaseVerification type="success" />
          )}
        </Card>
      </div>
    </BaseLayout>
  );
};

export default CertificationPurchase;
