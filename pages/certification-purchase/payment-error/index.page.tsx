import { useRouter } from 'next/router';

import Card from '@/components/Card/Card';
import BaseLayout from '@/components/Layouts/BaseLayout/BaseLayout';
import FirstTimePurchaseVerification from '@/pages/certification-purchase/components/FirstTimePurchaseVerification/FirstTimePurchaseVerification';

import styles from '../certification-purchase.module.css';

const CertificationPurchase = (): JSX.Element => {
  const router = useRouter();

  const errorType = router.query.errorType as string;

  return (
    <BaseLayout isAuthPage={false}>
      <div className={styles.wrapper} id="test">
        <Card
          size="2xl"
          radius="xs"
          borderColor="gray100"
          shadow="xs"
          bg="gray100"
        >
          <FirstTimePurchaseVerification
            type="failure"
            errorType={errorType || 'call_issuer'}
          />
        </Card>
      </div>
    </BaseLayout>
  );
};

export default CertificationPurchase;
