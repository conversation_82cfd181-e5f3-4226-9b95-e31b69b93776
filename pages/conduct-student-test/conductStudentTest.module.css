.wrapper {
  height: 100vh;
  background: linear-gradient(136.27deg, #1590c1 9.59%, #3a9ea7 56.18%);
  padding: var(--spacing-xl);
  overflow: hidden;
}

.elementsWrapper {
  max-width: 1040px;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin: 0 auto;
}

@media (max-width: 1040px) {
  .elementsWrapper {
    justify-content: center;
    flex-wrap: wrap;
  }
}

.cardWrapper {
  max-width: 610px;
  max-height: 440px;
  display: flex;
}

.baseWrapper {
  width: 100%;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
}

.promoWrapper {
  max-width: 400px;
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  gap: 12px;
}

@media (max-width: 1040px) {
  .promoWrapper {
    align-items: center;
  }

  .promoWrapper h2 {
    text-align: center !important;
  }
}

.divider {
  margin: 24px 0;
  width: 100px;
  background: var(--color-white);
  opacity: 0.4;
}

@media (max-width: 1040px) {
  .divider {
    width: 100%;
    margin: 0 24px;
  }
}

@media (max-width: 575px) {
  .divider {
    margin: 16px 0;
  }
}

.pinWrapper {
  height: 100px;
  display: flex;
  justify-content: center;
  flex-direction: column;
  gap: 8px;
}

.codeError {
  color: var(--color-error);
}

.pinInput {
  height: 70px;
  width: 70px;
}

.baseInput {
  height: 70px !important;
  width: 70px !important;
}

.gradeWrapper {
  display: flex;
  justify-content: flex-end;
  position: absolute;
  top: -50px;
  right: -35px;
}

.welcomeWrapper {
  position: relative;
  width: 100%;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: space-between;
}

.welcomeTitle {
  font-size: 54px;
}
