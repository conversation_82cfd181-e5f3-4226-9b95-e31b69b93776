import { Box, Container, Grid, Group, Image, Stack } from '@mantine/core';
import { useRouter } from 'next/router';
import { useState } from 'react';

// import Link from 'next/link';
import Button from '@/components/Button/Button';
import {
  InfoModal,
  ModalState,
} from '@/components/Modals/InformationModal/InfoModal';
import Text from '@/components/Text/Text';

import logo from '../public/images/logo.svg';
import mathProD from '../public/images/mathpro-d.svg';
import mathProS from '../public/images/mathpro-s.svg';
import tabletImage from '../public/images/tablet-screen.svg';
import classes from './index.module.css';

const IndexPage = (): JSX.Element => {
  const router = useRouter();
  const [modalState, setModalState] = useState<ModalState>({
    opened: false,
    title: '',
    content: '',
  });

  const handleOpenModal = (
    title: string,
    content: string | React.ReactNode
  ) => {
    setModalState({
      opened: true,
      title,
      content,
    });
  };

  const handleCloseModal = () => {
    setModalState((prev: ModalState) => ({ ...prev, opened: false }));
  };

  const mathProSText =
    'The first control gate is implemented with the administration of the short version of the Mathematical Profile Test (MathPro-S Test). The MathPro-S Test consists of four subscales for Grade 1 and five subscales for Grades 2 - 6 that assess different mathematical skills from each other and aims to outline the basic elements (or a "miniature") of the mathematical profile of the students as well as to identify students who are struggling in mathematics or who are at risk of learning difficulties in mathematics - Dyscalculia.';

  const mathProDText =
    'The second control gate takes place through the administration of the of the Mathematical Profile Diagnostic Test (full version). The MathPro-D Test is an online assessment tool for assessing the mathematical skills of Grade 1-6 students.It is a self-administered online tool, which can be administered individually or in groups.  It includes 18 subscales which are categorised into mathematical skills based either on specific numerical cognitive systems (number sense) or general domain cognitive skills (memory, visuo-spatial, reasoning).The MathPro Test is considered a reliable and valid tool which can be used both for large-scale studies and for the detailed assessment of the mathematical profile of children with (or without) learning difficulties in mathematics - dyscalculia in order to be used for diagnostic purposes. The in-depth assessment provided by the MathPro Test can be a key tool for diagnosing specific individual mathematical skills deficits.  Such an approach is in line with the prevailing view according to more recent scientific findings, which argue that Learning Difficulties in Mathematics (MLD) - Dyscalculia present heterogeneity and therefore should be assessed at an individual level. This is also in line with the way of diagnosing MDM-Dyscalculia recommended by the DSM-V (Diagnostic and statistical manual of mental Disorders) with a parallel assessment of additional cognitive and non-cognitive factors by a multidisciplinary team of specialists.';

  const mathProSContent = (
    <>
      {/* This Group shows only on larger screens */}
      <Group align="flex-start" gap="32px" wrap="nowrap" visibleFrom="sm">
        <Image
          src={mathProS.src}
          alt="MathPro S Test"
          className={classes.modalImage}
          style={{ flexShrink: 0 }}
        />
        <Text untranslatedText={mathProSText} />
      </Group>

      {/* This Stack shows only on mobile */}
      <Stack gap="md" hiddenFrom="sm">
        <Image
          src={mathProS.src}
          className={classes.modalImage}
          alt="MathPro S Test"
          style={{ margin: '0 auto' }}
        />
        <Text untranslatedText={mathProSText} />
      </Stack>
    </>
  );

  const mathProDContent = (
    <>
      {/* This Group shows only on larger screens */}
      <Group align="flex-start" gap="32px" wrap="nowrap" visibleFrom="sm">
        <Image
          src={mathProD.src}
          alt="MathPro D Test"
          className={classes.modalImage}
          style={{ flexShrink: 0 }}
        />
        <Text untranslatedText={mathProDText} />
      </Group>

      {/* This Stack shows only on mobile */}
      <Stack gap="md" hiddenFrom="sm">
        <Image
          src={mathProD.src}
          className={classes.modalImage}
          alt="MathPro D Test"
          style={{ margin: '0 auto' }}
        />
        <Text untranslatedText={mathProDText} />
      </Stack>
    </>
  );

  return (
    <Box style={{ position: 'relative' }}>
      <Container
        style={{
          maxWidth: 1108,
          position: 'relative',
        }}
      >
        <Image src={logo.src} alt="Math Pro Logo" className={classes.logo} />
      </Container>

      {/* Hero section */}
      <Box className={classes.hero}>
        <Container size="lg" py={50} className={classes.imageContainer}>
          <Group align="center" justify="space-between" gap={40}>
            <Stack gap="xl" style={{ flex: 1 }}>
              <Text
                type="h1"
                className={classes.title}
                untranslatedText="Teach mathematics based on student profiles"
              />
              <Group gap="md">
                <Button
                  transKey="student-capital"
                  onClick={() => router.push('conduct-student-test')}
                />
                <Button
                  transKey="educator-capital"
                  onClick={() => router.push('auth/login')}
                />
              </Group>
            </Stack>
            <div />
          </Group>
        </Container>
      </Box>

      {/* About section */}
      <Box className={classes.aboutSection}>
        <Container size="lg">
          <Group align="flex-start" justify="space-between" gap={40}>
            <Stack className={classes.aboutContent}>
              <Text
                untranslatedText="What is MathPro?"
                color="white"
                type="h2"
              />
              <Text
                className={classes.aboutText}
                color="white"
                untranslatedText="
                MathPro Education is a cutting-edge digital platform dedicated
                to assessing and enhancing students' mathematical skills—whether
                they face learning difficulties, such as dyscalculia, or not.
                Our mission extends beyond students, offering specialized
                training for teachers and education professionals to improve
                math instruction and intervention."
              />
            </Stack>

            <div className={classes.tabletContainer}>
              <Image
                src={tabletImage.src}
                alt="MathPro Platform Interface"
                className={classes.tabletImage}
              />
            </div>
          </Group>
        </Container>
      </Box>

      {/* Features section */}
      <Box className={classes.featuresSection}>
        <Container size="lg">
          <Grid gutter={50}>
            <Grid.Col span={{ base: 12, sm: 6 }}>
              <div className={classes.featureBlock}>
                <Text
                  type="h2"
                  mb={20}
                  className={classes.featureTitle}
                  untranslatedText="Assessment & Diagnosis"
                />
                <Text
                  untranslatedText="
                  MathPro Education collaborates with leading universities
                  worldwide to create scientifically validated psychometric
                  tools. These tools help identify students&#39; mathematical
                  strengths and weaknesses, assess math skills at the school
                  level, and diagnose learning difficulties in mathematics -
                  dyscalculia, through expert evaluation."
                />
              </div>
            </Grid.Col>

            <Grid.Col span={{ base: 12, sm: 6 }}>
              {/* Products row */}
              <div className={classes.productsRow}>
                <div className={classes.productCard}>
                  <Image
                    src={mathProS.src}
                    alt="MathPro S Test"
                    className={classes.certificateImage}
                  />
                  <Button
                    transKey="learn-more-capital"
                    onClick={() =>
                      handleOpenModal('MathPro S Test', mathProSContent)
                    }
                  />
                </div>

                <div className={classes.productCard}>
                  <Image
                    src={mathProD.src}
                    alt="MathPro D Test"
                    className={classes.certificateImage}
                  />
                  <Button
                    transKey="learn-more-capital"
                    onClick={() =>
                      handleOpenModal('MathPro D Test', mathProDContent)
                    }
                  />
                </div>

                <InfoModal
                  {...modalState}
                  onClose={handleCloseModal}
                  size="xl"
                />
              </div>
            </Grid.Col>

            <Grid.Col span={{ base: 12, sm: 6 }}>
              <div className={classes.featureBlock}>
                <Text
                  type="h2"
                  mb={20}
                  className={classes.featureTitle}
                  untranslatedText="Instruction & Intervention"
                />
                <Text
                  untranslatedText="
                  Our educational products support differentiated math
                  instruction in classrooms, promoting the inclusion of students
                  with math difficulties or dyscalculia. Additionally, we
                  provide targeted intervention programs tailored to the
                  individualized mathematical profiles of students with
                  dyscalculia, helping them build essential math skills
                  effectively."
                />
              </div>
            </Grid.Col>

            <Grid.Col span={{ base: 12, sm: 6 }}>
              <div className={classes.featureBlock}>
                <Text
                  type="h2"
                  mb={20}
                  className={classes.featureTitle}
                  untranslatedText="Training & Certification"
                />
                <Text
                  untranslatedText="
                  MathPro Education offers specialized training and
                  certification programs for educators and professionals in
                  administering our assessment and diagnostic tools. We also
                  provide professional development courses on differentiated
                  math instruction for primary and secondary education,
                  following the principles of Universal Design for Learning. Our
                  training programs are available in-person at schools and
                  institutions or online, through both synchronous and
                  asynchronous learning formats."
                />
                <Text
                  untranslatedText="
                  Join MathPro Education in transforming math learning and
                  instruction with evidence-based, innovative solutions."
                />
              </div>
            </Grid.Col>
          </Grid>
        </Container>
      </Box>

      {/* Footer */}
      <Box className={classes.footer} />
    </Box>
  );
};

export default IndexPage;
