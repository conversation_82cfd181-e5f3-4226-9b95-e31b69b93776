import { useQuery } from '@tanstack/react-query';
import { createContext, useContext, useMemo } from 'react';

import QUERY_KEYS from '@/common/queryKeys';
import PURCHASES from '@/services/purchases';
import { VatCountriesListType } from '@/types/common';

import { UserContext } from './UserProvider';

type SupportedCountriesContextType = {
  supportedBillingCountries: VatCountriesListType;
  isLoadingVatCountriesList: boolean;
};

const defaultContextData: SupportedCountriesContextType = {
  supportedBillingCountries: [],
  isLoadingVatCountriesList: false,
};

const CountriesContextData = createContext(defaultContextData);

export const SupportedBillingCountriesProvider = ({
  children,
}: {
  children: React.ReactNode;
}) => {
  const { user } = UserContext();

  const isUserLoggedIn = Boolean(user.id);
  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  const { data: vatCountriesList, isLoading: isLoadingVatCountriesList } =
    useQuery<VatCountriesListType | null | undefined>({
      enabled:
        isUserLoggedIn &&
        (user.roles.includes('admin') ||
          user.roles.includes('schoolAdmin') ||
          user.roles.includes('teacher')),
      queryFn: PURCHASES.GET_VAT_COUNTRIES_LIST,
      queryKey: [QUERY_KEYS.VAT_COUNTRIES_LIST],
    });

  const contextValue = useMemo(
    () => ({
      supportedBillingCountries: vatCountriesList || [],
      isLoadingVatCountriesList,
    }),
    [vatCountriesList, isLoadingVatCountriesList]
  );

  return (
    <CountriesContextData.Provider value={contextValue}>
      {children}
    </CountriesContextData.Provider>
  );
};

export const SupportedCountriesContext = () => {
  return useContext(CountriesContextData);
};
