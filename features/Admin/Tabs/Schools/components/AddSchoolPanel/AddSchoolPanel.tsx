/* eslint-disable no-param-reassign */
import { notifications } from '@mantine/notifications';
import { useMutation, useQueryClient } from '@tanstack/react-query';
import { useState } from 'react';
import { useTranslation } from 'react-i18next';

import { SCHOOLS_ERRORS } from '@/common/errors';
import QUERY_KEYS from '@/common/queryKeys';
import ADMIN_SCHOOLS from '@/services/admin/schools';

import SchoolForm from '../../forms/SchoolForm';

type AddSchoolPanelProps = {
  onCloseModal: () => void;
};

const AddSchoolPanel = ({ onCloseModal }: AddSchoolPanelProps): JSX.Element => {
  const { t } = useTranslation();
  const queryClient = useQueryClient();
  const [isInvalidVat, setIsInvalidVat] = useState(false);

  const addSchoolMutation = useMutation({
    mutationFn: ADMIN_SCHOOLS.ADD_SCHOOL,
    onSuccess: () => {
      queryClient.invalidateQueries({
        queryKey: [QUERY_KEYS.ADMIN_SCHOOLS],
      });

      queryClient.invalidateQueries({
        queryKey: [QUERY_KEYS.ADMIN_UNASSIGNED_SCHOOL_ADMINS],
      });

      onCloseModal();
    },
    onError: (error) => {
      if (error.message === SCHOOLS_ERRORS.INVALID_TAX_ID) {
        setIsInvalidVat(true);
      } else {
        notifications.show({
          title: t('error'),
          message: t(error.message),
          color: 'red',
        });
      }
    },
  });

  return (
    <SchoolForm
      defaultValues={{
        name: '',
        code: '',
        email: '',
        phoneNumber: '',
        url: '',
        administrator: null,
        type: '',
        addressLine1: '',
        vatNumber: '',
        city: '',
        postcode: '',
        country: '',
      }}
      submitCallback={addSchoolMutation.mutate}
      isLoading={addSchoolMutation.isPending}
      onCloseModal={onCloseModal}
      type="add"
      isInvalidVat={isInvalidVat}
      updateInvalidVat={setIsInvalidVat}
    />
  );
};

export default AddSchoolPanel;
