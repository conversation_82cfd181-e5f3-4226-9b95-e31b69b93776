/* eslint-disable no-param-reassign */
import { zodResolver } from '@hookform/resolvers/zod';
import { Box, TextInput } from '@mantine/core';
import { notifications } from '@mantine/notifications';
import { useMutation } from '@tanstack/react-query';
import countries from 'i18n-iso-countries';
import React, { useState } from 'react';
import { Controller, useForm } from 'react-hook-form';
import { useTranslation } from 'react-i18next';
import { BiArrowBack } from 'react-icons/bi';

import Button from '@/components/Button/Button';
import Card from '@/components/Card/Card';
import CloseButton from '@/components/CloseButton/CloseButton';
import SelectDropdown from '@/components/SelectDropdown/SelectDropdown';
import Text from '@/components/Text/Text';
import { SupportedCountriesContext } from '@/context/SupportedBillingCountriesProvider';
import useUnassignedSchoolAdmins from '@/features/Admin/adminHooks/useUnassignedSchoolAdmins';
import ADMIN_SCHOOLS from '@/services/admin/schools';
import { SCHOOL_FORM_SCHEMA } from '@/zod/zodFormValidationSchemas';

import { SchoolFormType } from '../types';
import LogoUpload from './LogoUpload';
import s from './SchoolForm.module.css';

type SchoolFormProps = {
  submitCallback: (data: unknown) => void;
  isLoading: boolean;
  defaultValues: SchoolFormType;
  administratorDetails?: {
    id: string;
    email: string | null;
    firstName: string | null;
    lastName: string | null;
  } | null;
  onCloseModal: () => void;
  type: 'add' | 'edit';
  isInvalidVat: boolean;
  updateInvalidVat: (arg: boolean) => void;
};

const SchoolForm = ({
  administratorDetails,
  defaultValues,
  isInvalidVat,
  isLoading,
  onCloseModal,
  submitCallback,
  type,
  updateInvalidVat,
}: SchoolFormProps) => {
  const { i18n, t } = useTranslation();
  const [isUploadingImage, setIsUploadingImage] = useState(false);

  const [isEditingAddress, setIsEditingAddress] = useState(false);

  const { supportedBillingCountries } = SupportedCountriesContext();

  const defaultImageToUpload = defaultValues.profilePicture || null;

  const [imageToUpload, setImageToUpload] = useState<
    | {
        imageFile: File;
        fileName: string;
      }
    | string
    | null
  >(defaultImageToUpload);

  const [imageImportError, setImageImportError] = useState<
    'file-too-large' | 'file-invalid-type' | null
  >(null);

  const { areUnassignedSchoolAdminsLoading, schoolAdmins } =
    useUnassignedSchoolAdmins();

  const {
    clearErrors,
    control,
    formState: { errors },
    handleSubmit,
    register,
    watch,
  } = useForm<SchoolFormType>({
    resolver: zodResolver(SCHOOL_FORM_SCHEMA),
    defaultValues,
    mode: 'onSubmit',
  });

  const addressErrorMessages =
    errors.addressLine1?.message ||
    errors.city?.message ||
    errors.postcode?.message ||
    errors.email?.message ||
    errors.country?.message ||
    (isInvalidVat && 'invalid-vat-number');

  const isAddressIncomplete = Boolean(addressErrorMessages);

  const isAddressFormFilled =
    watch('addressLine1') &&
    watch('city') &&
    watch('postcode') &&
    watch('email') &&
    watch('country');

  const SCHOOL_TYPES = [
    {
      label: t('public'),
      value: 'public',
    },
    {
      label: t('private'),
      value: 'private',
    },
    {
      label: t('church'),
      value: 'church',
    },
  ];

  const SCHOOL_ADMINS_LIST = [
    ...(administratorDetails?.id
      ? [
          {
            label: `${administratorDetails.firstName || ''} ${administratorDetails.lastName || ''}`,
            value: administratorDetails.id,
          },
        ]
      : []),
    ...schoolAdmins.map((admin) => ({
      label: `${admin.firstName} ${admin.lastName}`,
      value: admin.id,
    })),
  ];

  const uploadLogoImage = useMutation({
    mutationFn: ADMIN_SCHOOLS.UPLOAD_LOGO_IMAGE,
    onError: (error) => {
      notifications.show({
        title: t(error.message),
        message: '',
        color: 'red',
      });

      setIsUploadingImage(false);
    },
    onSuccess: (res) => {
      if (res) {
        submitCallback({
          name: watch('name') || '',
          code: watch('code') || '',
          email: watch('email') || '',
          phoneNumber: watch('phoneNumber') || '',
          url: watch('url') || '',
          administrator: watch('administrator') || '',
          type: watch('type') || '',
          address: {
            addressLine1: watch('addressLine1') || '',
            city: watch('city') || '',
            postcode: watch('postcode') || '',
            country: watch('country') || '',
            vatNumber: watch('vatNumber') || '',
          },
          profilePicture: res,
        });

        setImageImportError(null);
        setIsUploadingImage(false);
      }
    },
  });

  const onSubmitSchoolForm = async (data: SchoolFormType) => {
    if (imageToUpload && typeof imageToUpload !== 'string') {
      setIsUploadingImage(true);
      uploadLogoImage.mutate({ profileImage: imageToUpload });
    } else {
      if (imageToUpload === null) {
        data.profilePicture = null;
      }

      submitCallback({
        name: data.name || '',
        code: data.code || '',
        email: data.email || '',
        phoneNumber: data.phoneNumber || '',
        url: data.url || '',
        administrator: data.administrator || '',
        type: data.type || '',
        address: {
          addressLine1: data.addressLine1 || '',
          city: data.city || '',
          postcode: data.postcode || '',
          country: data.country || '',
          vatNumber: data.vatNumber.length > 0 ? data.vatNumber : null,
        },
        profilePicture: data.profilePicture || null,
      });
    }
  };

  const clearVatNumberError = () => {
    if (isInvalidVat) {
      updateInvalidVat(false);
      clearErrors('vatNumber');
    }
  };

  return (
    <Card
      size="xl"
      bg="gray50"
      isLoading={isLoading}
      className={`${s.wrapper} ${isEditingAddress ? s.addressWrapper : s.infoWrapper}`}
    >
      <div className={s.header}>
        {isEditingAddress ? (
          <Box
            className={s.arrowAndTextWrapper}
            onClick={() => setIsEditingAddress(false)}
          >
            <BiArrowBack fontSize={24} color="black" className={s.pointer} />

            <Text transKey="edit-address" type="h3" fw={250} />
          </Box>
        ) : (
          <Text
            transKey={type === 'edit' ? 'edit-school' : 'new-school'}
            type="h3"
          />
        )}

        {isEditingAddress ? (
          <Button
            onClick={() => setIsEditingAddress(false)}
            type="button"
            transKey="save-capital"
            isDisabled={isAddressIncomplete || !isAddressFormFilled}
          />
        ) : (
          <CloseButton onClick={onCloseModal} variant="outlined" />
        )}
      </div>

      {isEditingAddress ? (
        <Card size="xl" className={s.schoolForm}>
          <Text transKey="address" type="h4" color="black" fw={300} mb={16} />

          <TextInput
            label={t('address')}
            placeholder={t('address')}
            error={t(errors.addressLine1?.message || '')}
            {...register('addressLine1')}
            className={s.inputWrapper}
          />

          <div className={s.row}>
            <TextInput
              label={t('city')}
              placeholder={t('city')}
              {...register('city')}
              error={t(errors.city?.message || '')}
              className={s.inputWrapper}
            />

            <TextInput
              label={t('post-code')}
              placeholder={t('post-code')}
              {...register('postcode')}
              error={t(errors.postcode?.message || '')}
              className={s.inputWrapper}
            />
          </div>

          <div className={s.row}>
            <TextInput
              label={t('school-email')}
              placeholder={t('school-email')}
              {...register('email')}
              error={t(errors.email?.message || '')}
              className={s.inputWrapper}
            />

            <TextInput
              label={t('phone-optional')}
              placeholder={t('phone')}
              {...register('phoneNumber')}
              className={s.inputWrapper}
            />
          </div>

          <div className={s.row}>
            <Controller
              name="country"
              control={control}
              render={({ field }) => (
                <SelectDropdown
                  value={field.value || ''}
                  label={t('country')}
                  isDisabled={areUnassignedSchoolAdminsLoading}
                  data={supportedBillingCountries.map((item) => ({
                    label:
                      countries.getName(
                        item.code.toLowerCase(),
                        i18n.language
                      ) || '',
                    value: item.code.toLowerCase(),
                  }))}
                  onChange={(v) => {
                    field.onChange(v);

                    if (isInvalidVat) {
                      clearVatNumberError();
                    }
                  }}
                  placeholder={t('country')}
                  clearable
                  error={t(errors.country?.message || '')}
                />
              )}
            />

            <TextInput
              label={t('vat-number')}
              placeholder={t('vat-number')}
              {...register('vatNumber')}
              className={s.inputWrapper}
              error={t(
                errors.vatNumber?.message ||
                  (isInvalidVat && 'invalid-vat-number') ||
                  ''
              )}
              onChange={() => {
                if (isInvalidVat) {
                  clearVatNumberError();
                }
              }}
            />
          </div>
        </Card>
      ) : (
        <form id="school-form" onSubmit={handleSubmit(onSubmitSchoolForm)}>
          <div className={s.body}>
            <LogoUpload
              imageImportError={imageImportError}
              imageToUpload={imageToUpload}
              updateImageImportError={setImageImportError}
              updateImageToUpload={setImageToUpload}
              defaultImageToUpload={defaultImageToUpload || null}
              isLoading={isLoading}
            />

            <Card size="xl">
              <Text transKey="info" type="h4" color="black" fw={300} mb={16} />

              <div className={s.schoolForm}>
                <div className={s.row}>
                  <TextInput
                    placeholder={t('name')}
                    label={t('name')}
                    error={t(errors.name?.message || '')}
                    {...register('name')}
                    style={{ width: '150%' }}
                  />

                  <TextInput
                    label={t('code')}
                    placeholder={t('code')}
                    error={t(errors.code?.message || '')}
                    {...register('code')}
                    className={s.inputWrapper}
                  />
                </div>

                <div className={s.addressRow}>
                  <TextInput
                    label={t('address')}
                    placeholder={t('address')}
                    error={
                      isAddressIncomplete ? t(addressErrorMessages || '') : ''
                    }
                    onClick={() => setIsEditingAddress(true)}
                    className={s.addressInput}
                    value={isAddressIncomplete ? '' : watch('addressLine1')}
                    onChange={() => {}}
                  />

                  <div
                    className={`${s.addressButtonWrapper} ${isAddressIncomplete && s.errorAddressButtonWrapper}`}
                  >
                    <Button
                      onClick={() => setIsEditingAddress(true)}
                      type="button"
                      transKey={
                        isAddressFormFilled ? 'edit-capital' : 'add-capital'
                      }
                    />
                  </div>
                </div>

                <TextInput
                  label={t('website-optional')}
                  placeholder="http://my-school.edu"
                  {...register('url')}
                  error={t(errors.url?.message || '')}
                  className={s.inputWrapper}
                />

                <div className={s.row}>
                  <Controller
                    name="administrator"
                    control={control}
                    render={({ field }) => (
                      <SelectDropdown
                        value={field.value || ''}
                        label={t('schoolAdmin')}
                        isDisabled={areUnassignedSchoolAdminsLoading}
                        data={SCHOOL_ADMINS_LIST}
                        onChange={(v) => {
                          field.onChange(v);
                        }}
                        placeholder={t('schoolAdmin')}
                        clearable
                      />
                    )}
                  />

                  <Controller
                    name="type"
                    control={control}
                    rules={{
                      required: t('required'),
                    }}
                    render={({ field }) => (
                      <SelectDropdown
                        value={field.value || ''}
                        label={t('type')}
                        data={SCHOOL_TYPES}
                        onChange={(v) => {
                          field.onChange(v);
                        }}
                        placeholder={t('type')}
                        error={t(errors.type?.message || '')}
                      />
                    )}
                  />
                </div>
              </div>
            </Card>
          </div>

          <Box className={s.actions}>
            <Button
              variant="primaryOutlined"
              transKey="cancel-capital"
              onClick={onCloseModal}
            />

            <Button
              transKey={type === 'edit' ? 'save-capital' : 'create-capital'}
              type="submit"
              isLoading={isLoading || isUploadingImage}
            />
          </Box>
        </form>
      )}
    </Card>
  );
};

export default SchoolForm;
