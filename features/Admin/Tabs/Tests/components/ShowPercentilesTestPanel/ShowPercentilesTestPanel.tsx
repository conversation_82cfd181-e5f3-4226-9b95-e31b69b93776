import { Divider, Flex } from '@mantine/core';

import Card from '@/components/Card/Card';
import CloseButton from '@/components/CloseButton/CloseButton';
import Icon from '@/components/Icon/Icon';
import PrimaryModal from '@/components/Modals/PrimaryModal/PrimaryModal';
import Text from '@/components/Text/Text';
import { TranslationKeysType } from '@/types/common';

import s from './ShowPercentilesTestPanel.module.css';

type ShowPercentilesTestPanelProps = {
  data: {
    type: string;
    uploadedAt: string;
  }[];
  isOpen: boolean;
  onClose: () => void;
};

const DISPLAYED_ENTRIES = ['domains', 'percentiles', 'tolerance'];

const ShowPercentilesTestPanel = ({
  data,
  isOpen,
  onClose,
}: ShowPercentilesTestPanelProps): JSX.Element => {
  return (
    <PrimaryModal
      isOpen={isOpen}
      content={
        <Card size="xl" bg="gray50" className={s.wrapper}>
          {/* HEADER */}
          <div className={s.header}>
            <div className={s.textWrapper}>
              <Text transKey="percentiles" type="h3" />
            </div>

            <CloseButton onClick={onClose} variant="outlined" />
          </div>

          <Card bg="white" radius="xs" size="3xl">
            {DISPLAYED_ENTRIES.map((item) => {
              const uploadedAt = data.find(
                (test) => test.type === item
              )?.uploadedAt;

              return (
                <>
                  <div key={item}>
                    <Flex align="center" mb={16}>
                      <Text
                        transKey={item as TranslationKeysType}
                        type="h4"
                        fw={400}
                        mr={16}
                      />

                      {uploadedAt && (
                        <Icon name="CheckMarkSvg" color="green" size="md" />
                      )}
                    </Flex>

                    <Flex>
                      <Text transKey="uploaded-at" type="body1" mr={16} />

                      <Text untranslatedText=":" type="body1" mr={16} />

                      <Text
                        untranslatedText={uploadedAt || '-'}
                        type="body1"
                        fw={300}
                      />
                    </Flex>
                  </div>

                  <Divider my="md" m="40px 0px" />
                </>
              );
            })}
          </Card>
        </Card>
      }
    />
  );
};

export default ShowPercentilesTestPanel;
