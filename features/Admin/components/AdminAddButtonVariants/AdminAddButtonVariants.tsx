import { useState } from 'react';

import Button from '@/components/Button/Button';
import PrimaryModal from '@/components/Modals/PrimaryModal/PrimaryModal';
import { TranslationKeysType } from '@/types/common';

import { ActiveTabType } from '../../Admin';
import { ADMIN_TAB_OPTIONS } from '../../consts/adminTabs';
import AddSchoolPanel from '../../Tabs/Schools/components/AddSchoolPanel/AddSchoolPanel';
import CreateNewStudyPanel from '../../Tabs/Studies/components/CreateNewStudyPanel/CreateNewStudyPanel';
import AddUserPanel from '../../Tabs/Users/<USER>/AddUserPanel/AddUserPanel';

type AdminAddButtonVariantsProps = {
  activeTab: Omit<ActiveTabType, 'tests'> | null;
  isValidToggleModal: boolean;
};

const BUTTON_ACTIONS_TRANSLATION_KEYS = {
  users: 'add-user-capital',
  studies: 'add-study-capital',
  schools: 'add-school-capital',
} as const;

const AdminAddButtonVariants = ({
  activeTab,
  isValidToggleModal,
}: AdminAddButtonVariantsProps): JSX.Element => {
  const [isModalOpen, setIsModalOpen] = useState(false);

  const onCloseModal = () => {
    setIsModalOpen(false);
  };

  const getButtonDescription = () => {
    switch (activeTab) {
      case ADMIN_TAB_OPTIONS.USERS:
        return BUTTON_ACTIONS_TRANSLATION_KEYS.users;
      case ADMIN_TAB_OPTIONS.STUDIES:
        return BUTTON_ACTIONS_TRANSLATION_KEYS.studies;
      case ADMIN_TAB_OPTIONS.SCHOOLS:
        return BUTTON_ACTIONS_TRANSLATION_KEYS.schools;
      default:
        return '';
    }
  };

  const displayedModalContentSelector = () => {
    switch (activeTab) {
      case ADMIN_TAB_OPTIONS.USERS:
        return <AddUserPanel onClose={onCloseModal} />;
      case ADMIN_TAB_OPTIONS.STUDIES:
        return <CreateNewStudyPanel onCloseModal={onCloseModal} />;
      case ADMIN_TAB_OPTIONS.SCHOOLS:
        return <AddSchoolPanel onCloseModal={onCloseModal} />;
      default:
        return null;
    }
  };

  return (
    <div className="actionButtonIndicator">
      {activeTab && (
        <Button
          transKey={getButtonDescription() as TranslationKeysType}
          isLocked={false}
          onClick={() => isValidToggleModal && setIsModalOpen(true)}
        />
      )}

      <PrimaryModal
        isOpen={isModalOpen}
        content={displayedModalContentSelector()}
      />
    </div>
  );
};

export default AdminAddButtonVariants;
