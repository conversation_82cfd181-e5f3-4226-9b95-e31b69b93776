import { Box, LoadingOverlay, SegmentedControl } from '@mantine/core';
import { useQuery } from '@tanstack/react-query';
import { useMemo, useState } from 'react';
import { useTranslation } from 'react-i18next';

import QUERY_KEYS from '@/common/queryKeys';
import Card from '@/components/Card/Card';
import CloseButton from '@/components/CloseButton/CloseButton';
import Text from '@/components/Text/Text';
import { UserContext } from '@/context/UserProvider';
import STUDENTS_SKILLS from '@/services/students/skills';
import { ArbitrarySkillType } from '@/types/common';

import s from './AddStudentPanel.module.css';
import AddStudentTab from './AddStudentTab/AddStudentTab';
import ImportStudentsFromFile from './ImportStudentsFromFileTab/ImportStudentsFromFile';

type AddStudentPanelPropsType = {
  onClosePanel: () => void;
};

const SEGMENTED_VALUES = {
  ADD_NEW_STUDENT: 'add-new-student',
  IMPORT_FROM_FILE: 'import-from-file',
} as const;

type AddStudentPanelTabType =
  (typeof SEGMENTED_VALUES)[keyof typeof SEGMENTED_VALUES];

type AddNewStudentTabType = {
  label: string;
  value: (typeof SEGMENTED_VALUES)[keyof typeof SEGMENTED_VALUES];
  isDisplayed: boolean;
};

// TODO ADD TYPE TO THE DEFAULT FORM VALUES
const DEFAULT_FORM_VALUES = {
  isAnonymous: false,
  classId: '',
  groupId: '',
  firstName: '',
  lastName: '',
  code: '',
  dateOfBirth: null,
  gender: null,
  schoolName: '',
  grade: null,
  residence: '',
};

const AddStudentPanel = ({
  onClosePanel,
}: AddStudentPanelPropsType): JSX.Element => {
  const { t } = useTranslation();
  const { user, userRoles } = UserContext();

  const { data: defaultStudentSkills, isLoading } = useQuery<
    ArbitrarySkillType[] | null
  >({
    queryFn: STUDENTS_SKILLS.GET_DEFAULT_STUDENT_SKILLS,
    queryKey: [QUERY_KEYS.DEFAULT_STUDENT_SKILLS],
    staleTime: Infinity,
    enabled: userRoles.isSchoolAdmin || userRoles.isIndependentTeacher,
  });

  const [selectedTab, setSelectedTab] = useState<AddStudentPanelTabType>(
    SEGMENTED_VALUES.ADD_NEW_STUDENT
  );

  const TABS: AddNewStudentTabType[] = useMemo(
    () => [
      {
        label: 'individual-capital',
        value: SEGMENTED_VALUES.ADD_NEW_STUDENT,
        isDisplayed:
          userRoles.isSchoolAdmin ||
          userRoles.isIndependentTeacher ||
          userRoles.isResearcher,
      },
      {
        label: 'group-capital',
        value: SEGMENTED_VALUES.IMPORT_FROM_FILE,
        isDisplayed:
          userRoles.isSchoolAdmin ||
          userRoles.isIndependentTeacher ||
          userRoles.isResearcher,
      },
    ],
    [userRoles]
  );

  return (
    <Box
      className={`
        ${s.wrapper}
        ${selectedTab === SEGMENTED_VALUES.IMPORT_FROM_FILE && s.importFromFileWrapper}
        ${selectedTab === SEGMENTED_VALUES.ADD_NEW_STUDENT && s.addStudentWrapper}

      `}
    >
      <Card size="xl" bg="gray50" isLoading={false}>
        <div className={s.header}>
          <div className={s.textWrapper}>
            <Text transKey="add-students" type="h3" />
          </div>

          {(userRoles.isSchoolAdmin ||
            userRoles.isIndependentTeacher ||
            userRoles.isResearcher) && (
            <SegmentedControl
              fullWidth
              withItemsBorders={false}
              value={selectedTab}
              onChange={(v) => setSelectedTab(v as AddStudentPanelTabType)}
              data={TABS.filter((tab) => tab.isDisplayed).map((item) => ({
                value: item.value,
                label: t(item.label),
              }))}

              // it has a styling issue, with the indicator on load
              // classNames={{
              //   indicator: s.segmentedIndicator,
              // }}
            />
          )}

          <div className={s.closeButton}>
            <CloseButton onClick={onClosePanel} variant="outlined" />
          </div>
        </div>

        <LoadingOverlay visible={isLoading} overlayProps={{ blur: 3 }} />

        {selectedTab === SEGMENTED_VALUES.ADD_NEW_STUDENT &&
          (((userRoles.isSchoolAdmin || userRoles.isIndependentTeacher) &&
            defaultStudentSkills) ||
            userRoles.isResearcher) && (
            <AddStudentTab
              onCloseModal={onClosePanel}
              defaultStudentsSkills={(defaultStudentSkills || []) as any}
              defaultStudentValues={{
                ...DEFAULT_FORM_VALUES,
                schoolName: userRoles.isIndependentTeacher
                  ? ''
                  : user?.school?.name || '',
              }}
            />
          )}

        {selectedTab === SEGMENTED_VALUES.IMPORT_FROM_FILE &&
          (userRoles.isSchoolAdmin ||
            userRoles.isIndependentTeacher ||
            userRoles.isResearcher) && (
            <ImportStudentsFromFile onCloseModal={onClosePanel} />
          )}
      </Card>
    </Box>
  );
};

export default AddStudentPanel;
