/* eslint-disable jsx-a11y/click-events-have-key-events */
/* eslint-disable jsx-a11y/no-static-element-interactions */
import Image from 'next/image';
import React, { useState } from 'react';

import Text from '@/components/Text/Text';
import { TranslationKeysType } from '@/types/common';

import s from './Elearning.module.css';
import PlayDigitToolModal from './PlayDigitToolModal/PlayDigitToolModal';
import { DigiTools } from './types';

type DigiToolsType = {
  type: DigiTools;
  title: string;
  description: string;
  image: string;
};

const CARDS: DigiToolsType[] = [
  {
    type: 'number-cards',
    title: 'playing-numbers',
    description: 'addition-facts-description',
    image: 'number-cards.svg',
  },
  {
    type: 'coins',
    title: 'coins',
    description: 'coins-description',
    image: 'coins.svg',
  },
  {
    type: 'times-table',
    title: 'times-table',
    description: 'times-table-description',
    image: 'times-table.svg',
  },
  {
    type: 'addition-facts',
    title: 'addition-facts',
    description: 'addition-facts-description',
    image: 'addition-facts.svg',
  },
  // {
  //   type: 'playing-cards',
  //   title: 'playing-cards',
  //   description: 'playing-cards-description',
  //   image: 'playing-cards.svg',
  // },
] as DigiToolsType[];

const Elearning = (): JSX.Element => {
  const [selectedDigiTool, setSelectedDigiTool] = useState<DigiTools>(null);

  return (
    <div className="routeWrapper">
      <div className={s.header}>
        <Text transKey="e-learning" type="h3" className="textPageIndicator" />
      </div>

      <div className={s.container}>
        <div className={s.cardsWrapper}>
          {CARDS.map(({ description, image, title, type }) => (
            <div
              key={title}
              className={s.card}
              onClick={() => {
                setSelectedDigiTool(type);
              }}
            >
              <Image
                src={`/images/${image}`}
                width={250}
                height={180}
                alt={title}
                className={s.cardImage}
              />
              <div className={s.cardDescription}>
                <Text
                  transKey={title as TranslationKeysType}
                  type="h4"
                  className={s.title}
                  fw={500}
                />

                {/* <Text
                  transKey={description as TranslationKeysType}
                  type="subTitle2"
                  fw={400}
                  mt={20}
                /> */}
              </div>
            </div>
          ))}
        </div>
      </div>

      <PlayDigitToolModal
        selectedDigiTool={selectedDigiTool}
        onCloseModal={() => setSelectedDigiTool(null)}
      />
    </div>
  );
};

export default Elearning;
