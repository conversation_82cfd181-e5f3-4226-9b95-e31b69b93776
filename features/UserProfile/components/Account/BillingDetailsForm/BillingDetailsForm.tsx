/* eslint-disable jsx-a11y/no-static-element-interactions */
/* eslint-disable jsx-a11y/click-events-have-key-events */
import { zodResolver } from '@hookform/resolvers/zod';
import { TextInput } from '@mantine/core';
import React, { useState } from 'react';
import { Controller, useForm } from 'react-hook-form';
import { useTranslation } from 'react-i18next';

import Radio from '@/components/Radio/Radio';
import SelectDropdown from '@/components/SelectDropdown/SelectDropdown';
import Text from '@/components/Text/Text';
import { SupportedCountriesContext } from '@/context/SupportedBillingCountriesProvider';
import { AddProfileAddressInvoiceFormType } from '@/types/common';
import {
  ADD_PROFILE_INVOICE_BILLING_FORM_SCHEMA,
  ADD_PROFILE_RECEIPT_BILLING_FORM_SCHEMA,
} from '@/zod/zodFormValidationSchemas';

import styles from './BillingDetailsForm.module.css';

type BillingDetailsFormProps = {
  defaultValues: AddProfileAddressInvoiceFormType;
  updateProfileMutation: any;
  isInvalidVat: boolean;
  updateInvalidVat: (arg: boolean) => void;
};

const BillingDetailsForm = ({
  defaultValues,
  isInvalidVat,
  updateInvalidVat,
  updateProfileMutation,
}: BillingDetailsFormProps) => {
  const { t } = useTranslation();
  const { isLoadingVatCountriesList, supportedBillingCountries } =
    SupportedCountriesContext();

  const [paymentMethod, setPaymentMethod] = useState<'receipt' | 'invoice'>(
    defaultValues.vatNumber ? 'invoice' : 'receipt'
  );

  const {
    clearErrors,
    control,
    formState: { errors },
    handleSubmit,
    register,
    setValue,
  } = useForm<AddProfileAddressInvoiceFormType>({
    resolver: zodResolver(
      paymentMethod === 'receipt'
        ? ADD_PROFILE_RECEIPT_BILLING_FORM_SCHEMA
        : ADD_PROFILE_INVOICE_BILLING_FORM_SCHEMA
    ),
    defaultValues,
    mode: 'onSubmit',
  });

  const clearVatNumberError = () => {
    if (isInvalidVat) {
      updateInvalidVat(false);
    }

    clearErrors('vatNumber');
  };

  const handleRadioButtonClick = (action: 'receipt' | 'invoice') => {
    setPaymentMethod(action);
    clearErrors('vatNumber');

    if (action === 'receipt') {
      setValue('vatNumber', '');
    } else {
      setValue('vatNumber', defaultValues.vatNumber);
    }
  };

  const onSubmitBillingDetailsForm = (
    data: AddProfileAddressInvoiceFormType
  ) => {
    updateProfileMutation.mutate({
      vatNumber: paymentMethod === 'invoice' ? data.vatNumber : null,
      address: {
        addressLine1: data.address?.addressLine1 || '',
        city: data.address?.city || '',
        postcode: data.address?.postcode || '',
        country: data.address?.country || '',
      },
    });
  };

  return (
    <div>
      <div className={styles.radioContainer}>
        <div
          className={styles.radioWrapper}
          onClick={() => {
            handleRadioButtonClick('receipt');
          }}
        >
          <Radio value="receipt" isChecked={paymentMethod === 'receipt'} />

          <Text transKey="individual" type="subTitle2" />
        </div>

        <div
          className={styles.radioWrapper}
          onClick={() => {
            handleRadioButtonClick('invoice');
          }}
        >
          <Radio value="invoice" isChecked={paymentMethod === 'invoice'} />

          <Text transKey="company" type="subTitle2" />
        </div>
      </div>

      <form
        className={styles.form}
        id="billing-details-form"
        onSubmit={handleSubmit(onSubmitBillingDetailsForm)}
      >
        <TextInput
          placeholder={t('address')}
          label={t('address')}
          error={t(errors.address?.addressLine1?.message || '')}
          {...register('address.addressLine1')}
        />

        <div className={styles.row}>
          <TextInput
            placeholder={t('city')}
            label={t('city')}
            {...register('address.city')}
            error={t(errors.address?.city?.message || '')}
            className={styles.smallInput}
          />

          <TextInput
            placeholder={t('post-code')}
            label={t('post-code')}
            {...register('address.postcode')}
            error={t(errors.address?.postcode?.message || '')}
            className={styles.smallInput}
          />
        </div>

        <div className={styles.row}>
          <Controller
            name="address.country"
            control={control}
            rules={{
              required: paymentMethod === 'invoice' && t('required'),
            }}
            render={({ field }) => (
              <SelectDropdown
                value={field.value || ''}
                label={t('country')}
                isDisabled={isLoadingVatCountriesList}
                data={
                  supportedBillingCountries?.map((item) => ({
                    label: item.name || '',
                    value: item.code || '',
                  })) || []
                }
                onChange={(v) => {
                  field.onChange(v);
                  clearVatNumberError();
                }}
                placeholder={t('country')}
                clearable
                error={t(errors.address?.country?.message || '')}
              />
            )}
          />

          <TextInput
            placeholder={t('vat-number')}
            label={t('vat-number')}
            {...register('vatNumber')}
            error={
              isInvalidVat
                ? t('invalid-vat-number')
                : t(errors.vatNumber?.message || '')
            }
            onChange={clearVatNumberError}
            disabled={paymentMethod === 'receipt'}
            className={styles.smallInput}
          />
        </div>
      </form>
    </div>
  );
};

export default BillingDetailsForm;
