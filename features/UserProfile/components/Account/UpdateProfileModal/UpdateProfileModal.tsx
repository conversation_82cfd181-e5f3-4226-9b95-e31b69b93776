/* eslint-disable no-param-reassign */
/* eslint-disable jsx-a11y/no-static-element-interactions */
/* eslint-disable jsx-a11y/click-events-have-key-events */
import { zodResolver } from '@hookform/resolvers/zod';
import { ActionIcon, SegmentedControl, TextInput } from '@mantine/core';
import { DateInput } from '@mantine/dates';
import { Dropzone, IMAGE_MIME_TYPE } from '@mantine/dropzone';
import { notifications } from '@mantine/notifications';
import { useMutation } from '@tanstack/react-query';
import Image from 'next/image';
import { useRef, useState } from 'react';
import { Controller, useForm } from 'react-hook-form';
import { useTranslation } from 'react-i18next';
import { RiDeleteBin5Fill } from 'react-icons/ri';

import { USERS_ERRORS } from '@/common/errors';
import { mantineDateParser, submitFormById } from '@/common/helpers';
import Button from '@/components/Button/Button';
import Card from '@/components/Card/Card';
import CloseButton from '@/components/CloseButton/CloseButton';
import Icon from '@/components/Icon/Icon';
import PrimaryModal from '@/components/Modals/PrimaryModal/PrimaryModal';
import Text from '@/components/Text/Text';
import { UserContext } from '@/context/UserProvider';
import PROFILES from '@/services/profiles';
import { ProfileFormValuesType } from '@/types/common';
import { PROFILE_FORM_SCHEMA } from '@/zod/zodFormValidationSchemas';

import BillingDetailsForm from '../BillingDetailsForm/BillingDetailsForm';
import s from './UpdateProfileModal.module.css';

type UpdateProfileModalPropsType = {
  isOpen: boolean;
  onClose: () => void;
};

const TABS = [
  {
    label: 'info-capital',
    value: 'info-form',
  },
  {
    label: 'billing-capital',
    value: 'billing-form',
  },
];

const UpdateProfileModal = ({
  isOpen,
  onClose,
}: UpdateProfileModalPropsType): JSX.Element => {
  const { isSchoolRole, user, userRoles } = UserContext();
  const { t } = useTranslation();
  const { invalidateUser } = UserContext();

  const { profilePicture } = user.profile;

  const defaultImageToUpload = profilePicture || null;

  const [imageToUpload, setImageToUpload] = useState<
    | {
        imageFile: File;
        fileName: string;
      }
    | string
    | null
  >(defaultImageToUpload);

  const [selectedTab, setSelectedTab] = useState(TABS[0].value);

  const [imageImportError, setImageImportError] = useState<
    'file-too-large' | 'file-invalid-type' | null
  >(null);

  const [isInvalidVat, setIsInvalidVat] = useState(false);

  const dropZoneRef = useRef<() => void>(null);

  const DEFAULT_FORM_VALUES = {
    firstName: user.profile.firstName,
    lastName: user.profile.lastName,
    dateOfBirth: user.profile.dob ? new Date(user.profile.dob) : null,
    phoneNumber: user.profile.phoneNumber || '',
    organisation: isSchoolRole
      ? user.school?.name || ''
      : user.profile.organisation || '',
    affiliation: user.profile.affiliation || '',
  };

  const {
    control,
    formState: { errors },
    getValues,
    handleSubmit,
    register,
    reset,
    watch,
  } = useForm<ProfileFormValuesType>({
    resolver: zodResolver(PROFILE_FORM_SCHEMA),
    defaultValues: DEFAULT_FORM_VALUES,
    mode: 'onSubmit',
  });

  const updateProfileMutation = useMutation({
    mutationFn: PROFILES.UPDATE_USER_PROFILE,
    onError: (error) => {
      if (error.message === USERS_ERRORS.INVALID_TAX_ID) {
        setIsInvalidVat(true);
      } else {
        notifications.show({
          title: t('error'),
          message: t(error.message),
          color: 'red',
        });
      }
    },
    onSuccess: (res) => {
      if (res) {
        invalidateUser();
        onClose();
        setImageImportError(null);
        setSelectedTab(TABS[0].value);
      }
    },
  });

  const uploadProfilePicture = useMutation({
    mutationFn: PROFILES.UPLOAD_PROFILE_PICTURE,
    onError: (error) => {
      notifications.show({
        title: t(error.message),
        message: '',
        color: 'red',
      });
    },
    onSuccess: (res) => {
      if (res) {
        // Get the current form values
        const formData = getValues();

        updateProfileMutation.mutate({
          ...formData,
          profilePicture: res,
        });
        invalidateUser();
        onClose();
        setImageImportError(null);
      }
    },
  });

  const onSubmitInfoForm = async (values: ProfileFormValuesType) => {
    if (imageToUpload && typeof imageToUpload !== 'string') {
      uploadProfilePicture.mutate({ profileImage: imageToUpload });
    } else {
      if (imageToUpload === null) {
        values.profilePicture = null;
      }

      updateProfileMutation.mutate(values);
    }
  };

  const displayedAvatarImage = () => {
    if (imageToUpload) {
      if (typeof imageToUpload === 'string') {
        return imageToUpload;
      }
      return URL.createObjectURL(imageToUpload.imageFile as File);
    }
    return defaultImageToUpload;
  };

  return (
    <PrimaryModal
      isOpen={isOpen}
      content={
        <Card
          size="2xl"
          bg="gray50"
          isLoading={updateProfileMutation.isPending}
          className={`${s.wrapper} ${selectedTab === 'billing-form' ? s.billingWrapper : s.infoWrapper}`}
          hasDynamicHeight
        >
          <div className={s.header}>
            <Text transKey="edit-your-profile" type="h3" />

            <SegmentedControl
              value={selectedTab}
              onChange={(v) => {
                setSelectedTab(v);
                reset(DEFAULT_FORM_VALUES);
              }}
              withItemsBorders={false}
              data={TABS.map((item) => ({
                value: item.value,
                label: t(item.label),
              }))}
            />

            <div className={s['header-actions']}>
              <Button
                transKey="save-capital"
                type="submit"
                onClick={() =>
                  submitFormById(
                    selectedTab === 'billing-form'
                      ? 'billing-details-form'
                      : 'profile-form'
                  )
                }
                isLoading={
                  updateProfileMutation.isPending ||
                  uploadProfilePicture.isPending
                }
              />

              <CloseButton
                onClick={() => {
                  onClose();
                  reset(DEFAULT_FORM_VALUES);
                  setImageToUpload(defaultImageToUpload);
                  setImageImportError(null);
                  setSelectedTab(TABS[0].value);
                }}
                variant="outlined"
              />
            </div>
          </div>

          {selectedTab === TABS[0].value && (
            <div className={s.body}>
              <Card size="xl" className={s.photoWrapper}>
                <div className={s.uploadHeaderWrapper}>
                  <Text transKey="photo" type="h4" color="black" fw={300} />

                  {imageToUpload && (
                    <ActionIcon
                      variant="filled"
                      color="transparent"
                      size="lg"
                      aria-label="Delete"
                      className={s.deleteButton}
                      onClick={(e) => {
                        e.stopPropagation();
                        setImageToUpload(null);
                      }}
                    >
                      <RiDeleteBin5Fill color="var(--color-danger)" size={19} />
                    </ActionIcon>
                  )}
                </div>

                <Dropzone
                  className={s['profile-photo-wrapper']}
                  onDrop={(file) => {
                    const fileName = file[0].name;

                    if (imageImportError) setImageImportError(null);

                    setImageToUpload({ fileName, imageFile: file[0] });
                  }}
                  accept={IMAGE_MIME_TYPE}
                  openRef={dropZoneRef}
                  activateOnClick
                  useFsAccessApi
                  maxFiles={1}
                  maxSize={5 * 1024 * 1024}
                  onReject={(file) => {
                    setImageImportError(
                      file[0].errors[0].code === 'file-too-large'
                        ? 'file-too-large'
                        : 'file-invalid-type'
                    );
                  }}
                  disabled={
                    updateProfileMutation.isPending ||
                    uploadProfilePicture.isPending
                  }
                  styles={{
                    inner: {
                      display: 'flex',
                      flexDirection: 'column',
                      alignItems: 'center',
                    },
                    root: {
                      paddingTop: '24px',
                      paddingBottom: '24px',
                    },
                  }}
                >
                  {imageToUpload ? (
                    <Image
                      src={displayedAvatarImage() || ''}
                      alt="profile photo"
                      width={120}
                      height={120}
                      className={s.imageAvatar}
                    />
                  ) : (
                    <Icon
                      name="ProfilePlaceholderSvg"
                      color="turquoise"
                      className={s.placeholderIcon}
                    />
                  )}

                  {imageImportError && (
                    <Text
                      transKey={imageImportError}
                      type="label"
                      color="danger"
                      mt={12}
                    />
                  )}

                  <Text
                    transKey="upload-photo"
                    type="button"
                    color="blue"
                    mt={42}
                  />
                </Dropzone>
              </Card>

              <Card size="xl">
                <form
                  id="profile-form"
                  className={s['profile-form']}
                  onSubmit={handleSubmit(onSubmitInfoForm)}
                >
                  <TextInput
                    placeholder={t('name')}
                    label={t('name')}
                    error={t(errors.firstName?.message || '')}
                    {...register('firstName')}
                  />

                  <TextInput
                    label={t('surname')}
                    placeholder={t('surname')}
                    error={t(errors.lastName?.message || '')}
                    {...register('lastName')}
                  />

                  <div className={s.row}>
                    <Controller
                      name="dateOfBirth"
                      control={control}
                      render={({ field }) => (
                        <DateInput
                          label={t('date-of-birth')}
                          placeholder={t('date-of-birth')}
                          clearable
                          allowDeselect
                          highlightToday
                          dateParser={mantineDateParser}
                          value={watch('dateOfBirth')}
                          valueFormat="DD/MM/YYYY"
                          onChange={(v) => {
                            field.onChange(new Date(v as Date));
                          }}
                          w="100%"
                        />
                      )}
                    />

                    <TextInput
                      label={t('phone')}
                      placeholder={t('phone')}
                      {...register('phoneNumber')}
                      w="100%"
                    />
                  </div>

                  <div className={s.row}>
                    <TextInput
                      label={t('affiliation')}
                      placeholder={t('affiliation')}
                      {...register('affiliation')}
                      maxLength={40}
                      w="100%"
                    />

                    <TextInput
                      label={isSchoolRole ? t('school') : t('organisation')}
                      placeholder={
                        isSchoolRole ? t('school') : t('organisation')
                      }
                      disabled={
                        userRoles.isSchoolAdmin || userRoles.isSchoolTeacher
                      }
                      w="100%"
                      {...register('organisation')}
                    />
                  </div>
                </form>
              </Card>
            </div>
          )}

          {selectedTab === TABS[1].value && (
            <div className={s.body}>
              <Card size="xl">
                <BillingDetailsForm
                  defaultValues={{
                    address: {
                      addressLine1: user?.profile?.address?.addressLine1 || '',
                      addressLine2: user?.profile?.address?.addressLine2 || '',
                      city: user?.profile?.address?.city || '',
                      country:
                        user?.profile?.address?.country?.toLowerCase() || '',
                      postcode: user?.profile?.address?.postcode || '',
                    },
                    vatNumber: user?.vatNumber || '',
                  }}
                  updateProfileMutation={updateProfileMutation}
                  isInvalidVat={isInvalidVat}
                  updateInvalidVat={setIsInvalidVat}
                />
              </Card>
            </div>
          )}
        </Card>
      }
    />
  );
};

export default UpdateProfileModal;
