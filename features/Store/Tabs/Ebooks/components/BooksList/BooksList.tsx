import { Box, Flex } from '@mantine/core';
import { cleanNotifications, notifications } from '@mantine/notifications';
import { useState } from 'react';
import { useTranslation } from 'react-i18next';

import PrimaryModal from '@/components/Modals/PrimaryModal/PrimaryModal';
import { CartContext } from '@/context/CartProvider';
import { PurchasesContext } from '@/context/PurchasesProvider';

import BookDetailsPanel from '../BookDetailsPanel/BookDetailsPanel';
import EBookBuyCard from '../EBookBuyCard/EBookBuyCard';

type BooksListProps = {
  books: any[];
};

const BooksList = ({ books }: BooksListProps): JSX.Element => {
  const { t } = useTranslation();
  const { openCheckout } = PurchasesContext();
  const { addToCart, isItemInCart, removeFromCart } = CartContext();
  const [selectedBook, setSelectedBook] = useState<any>(null);

  const onAddToCart = (product: any) => {
    cleanNotifications();
    notifications.show({
      title: `${product.name || ''} ${t(product.kind || '')}`,
      message: t('product-added-to-cart'),
      color: 'green',
    });

    addToCart(product);
  };

  const onRemoveFromCart = (stripeId: string) => {
    cleanNotifications();
    notifications.show({
      title: 'Product removed from cart',
      message: 'Product removed from cart',
      color: 'red',
    });

    removeFromCart(stripeId);
  };

  return (
    <Box>
      <Flex direction="column" gap={32}>
        {books.map((book) => (
          <EBookBuyCard
            key={book.stripeId}
            // eslint-disable-next-line @typescript-eslint/no-explicit-any
            productDetails={book as any}
            isDisabled={false}
            tooltipText="contact-admin-to-buy"
            onAddToCart={() => onAddToCart(book)}
            onRemoveFromCart={() => onRemoveFromCart(book.stripeId)}
            isAddedToCart={isItemInCart(book.stripeId)}
            onCardClick={() => setSelectedBook(book)}
            onBuy={() => openCheckout([book as any])}
          />
        ))}
      </Flex>

      <PrimaryModal
        isOpen={selectedBook !== null}
        content={
          selectedBook?.stripeId ? (
            <BookDetailsPanel
              book={selectedBook}
              isAddedToCart={isItemInCart(selectedBook.stripeId)}
              onAddToCart={() => onAddToCart(selectedBook)}
              onRemoveFromCart={() => onRemoveFromCart(selectedBook.stripeId)}
              onClosePanel={() => setSelectedBook(null)}
            />
          ) : null
        }
      />
    </Box>
  );
};

export default BooksList;
