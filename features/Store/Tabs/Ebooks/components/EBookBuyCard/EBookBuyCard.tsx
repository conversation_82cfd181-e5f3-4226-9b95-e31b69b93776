import { Box, Tooltip } from '@mantine/core';
import Image from 'next/image';
import React from 'react';

import { BLUR_IMAGE_SVG, CURRENCY_SYMBOLS, PRODUCTS } from '@/common/consts';
import { getFormattedPrice } from '@/common/helpers';
import Button from '@/components/Button/Button';
import Card from '@/components/Card/Card';
import IconButton from '@/components/IconButton/IconButton';
import Text from '@/components/Text/Text';
// import { PurchasesContext } from '@/context/PurchasesProvider';
import { ProductDetailsType } from '@/types/common';

import styles from './EBookBuyCard.module.css';

type EBookBuyCardProps = {
  productDetails: ProductDetailsType;
  isDisabled?: boolean;
  tooltipText?: string;
  isAddedToCart: boolean;
  onAddToCart?: () => void;
  onRemoveFromCart?: () => void;
  onCardClick?: () => void;
  onBuy?: () => void;
};

const EBookBuyCard = ({
  isAddedToCart,
  isDisabled = false,
  onAddToCart,
  onBuy,
  onCardClick,
  onRemoveFromCart,
  productDetails,
  tooltipText = '',
}: EBookBuyCardProps) => {
  // const { openCheckout } = PurchasesContext();

  // Fall back Image
  // const image = productDetails?.type
  //   ? `/images/tests/${PRODUCTS[productDetails.type].certificateImage}`
  //   : '';

  return (
    <Card
      size="sm"
      radius="sm"
      borderSize={2}
      borderColor="gray50"
      shadow="none"
    >
      <Box className={styles.wrapper} onClick={onCardClick}>
        <div className={styles.testContent}>
          <Image
            src={
              productDetails.images[0]
              // fallback Image  || image
            }
            alt="product"
            width={131}
            height={131}
            placeholder="blur"
            blurDataURL={BLUR_IMAGE_SVG}
          />

          <div className={styles.descriptionWrapper}>
            <Text untranslatedText={productDetails.name} type="h3" />

            <Text
              transKey="typeCardDescription"
              type="subTitle2"
              transVariables={{
                productType: productDetails.name,
              }}
            />
          </div>
        </div>

        <Tooltip label={tooltipText} disabled={!isDisabled}>
          <div className={styles.buttonsWrapper}>
            <Button
              transKey="buy-capital"
              isDisabled={isDisabled}
              leftSection={
                <span className={styles.price}>
                  <Text
                    untranslatedText={`${CURRENCY_SYMBOLS[productDetails.price.currency]}${getFormattedPrice(productDetails.price.amount)}`}
                    color="white"
                    fw={300}
                    type="body1"
                  />
                </span>
              }
              // onClick={() => openCheckout([productDetails])}
              onClick={onBuy}
              // onClick={() => openCheckout([])}
            />

            <IconButton
              variant={isAddedToCart ? 'dangerOutlined' : 'primary'}
              iconName={isAddedToCart ? 'CloseSvg' : 'CartAddProductSvg'}
              iconSize={isAddedToCart ? 16 : 20}
              isDisabled={isDisabled}
              onClick={isAddedToCart ? onRemoveFromCart : onAddToCart}
              color={isAddedToCart ? 'red' : 'white'}
            />
          </div>
        </Tooltip>
      </Box>
    </Card>
  );
};

export default EBookBuyCard;
