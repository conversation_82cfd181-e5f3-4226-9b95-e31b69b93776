/* eslint-disable @typescript-eslint/no-unused-vars */
import { Box, Flex, ScrollArea, Tooltip } from '@mantine/core';
import Image from 'next/image';
import React from 'react';

import { BLUR_IMAGE_SVG, CURRENCY_SYMBOLS } from '@/common/consts';
// import { getFormattedPrice } from '@/common/helpers';
import Button from '@/components/Button/Button';
import Card from '@/components/Card/Card';
import CloseButton from '@/components/CloseButton/CloseButton';
import IconButton from '@/components/IconButton/IconButton';
import Text from '@/components/Text/Text';

import s from './BookDetailsPanel.module.css';

type BookDetailsPanelProps = {
  book: any;
  isAddedToCart: boolean;
  onAddToCart: () => void;
  onRemoveFromCart: () => void;
  onClosePanel: () => void;
  isDisabled?: boolean;
};

const BookDetailsPanel = ({
  book,
  isAddedToCart,
  isDisabled = false,
  onAddToCart,
  onClosePanel,
  onRemoveFromCart,
}: BookDetailsPanelProps): JSX.Element => {
  return (
    <Card
      size="2xl"
      radius="sm"
      borderSize={1}
      shadow="lg"
      bg="gray50"
      className={s.wrapper}
    >
      <Flex justify="space-between" mb={32}>
        <Box className={s.testContent}>
          <Image
            src={
              book.images[0]
              // fallback Image  || image
            }
            alt="product"
            width={160}
            height={180}
            placeholder="blur"
            blurDataURL={BLUR_IMAGE_SVG}
          />

          <Box className={s.descriptionWrapper}>
            <Text untranslatedText={book.name} type="h3" />

            <Text
              transKey="typeCardDescription"
              type="subTitle2"
              transVariables={{
                productType: book.name,
              }}
            />
          </Box>
        </Box>

        <Flex gap={16} align="center">
          <Button
            transKey="buy-capital"
            isDisabled={isDisabled}
            leftSection={
              <span className={s.price}>
                <Text
                  //   untranslatedText={`${CURRENCY_SYMBOLS[book.price.currency]}${getFormattedPrice(book.price.amount)}`}
                  untranslatedText="$12,322.12"
                  color="white"
                  fw={300}
                  type="body1"
                />
              </span>
            }
            // onClick={() => openCheckout([productDetails])}
            // onClick={() => openCheckout([])}
          />

          <IconButton
            variant={isAddedToCart ? 'dangerOutlined' : 'primary'}
            iconName={isAddedToCart ? 'CloseSvg' : 'CartAddProductSvg'}
            iconSize={isAddedToCart ? 16 : 20}
            isDisabled={isDisabled}
            onClick={isAddedToCart ? onRemoveFromCart : onAddToCart}
            color={isAddedToCart ? 'red' : 'white'}
          />

          <CloseButton onClick={onClosePanel} variant="outlined" />
        </Flex>
      </Flex>

      <Card size="lg" radius="xs" shadow="md" bg="white">
        <ScrollArea.Autosize type="auto" scrollbarSize={4} mah={400}>
          <Flex mb={32} direction="column">
            <Text transKey="description" mb={16} />

            <Text untranslatedText="Lorem ipsum dolor sit amet, consectetur adipiscing elit, sed do eiusmod tempor incididunt ut labore et dolore magna aliqua. Ut enim ad minim ..." />
          </Flex>

          <Flex mb={32} direction="column">
            <Text transKey="description" mb={16} />

            <Text untranslatedText="Lorem ipsum dolor sit amet, consectetur adipiscing elit, sed do eiusmod tempor incididunt ut labore et dolore magna aliqua. Ut enim ad minim veniam, quis nostrud exercitation ullamco laboris nisi ut aliquip ex ea commodo consequat. Duis aute irure dolor in reprehenderit in voluptate velit esse cillum dolore eu fugiat nulla pariatur. Excepteur sint occaecat cupidatat non proident, sunt in culpa qui officia deserunt mollit anim id est laborum." />
          </Flex>

          <Flex direction="column">
            <Text transKey="description" mb={16} />

            <Text untranslatedText="Lorem ipsum dolor sit amet, consectetur adipiscing elit, sed do eiusmod tempor incididunt ut labore et dolore magna aliqua. Ut enim ad minim veniam, quis nostrud exercitation ullamco laboris nisi ut aliquip ex ea commodo consequat. Duis aute irure dolor in reprehenderit in voluptate velit esse cillum dolore eu fugiat nulla pariatur. Excepteur sint occaecat cupidatat non proident, sunt in culpa qui officia deserunt mollit anim id est laborum." />
          </Flex>
        </ScrollArea.Autosize>
      </Card>
    </Card>
  );
};

export default BookDetailsPanel;
