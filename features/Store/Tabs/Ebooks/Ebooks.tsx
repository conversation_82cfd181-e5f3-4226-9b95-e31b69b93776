import { useState } from 'react';

import Checkbox from '@/components/Checkbox/Checkbox';
import Text from '@/components/Text/Text';

import BooksLayout from './components/BooksLayout/BooksLayout';
import BooksList from './components/BooksList/BooksList';

type FiltersType = {
  class: string[];
  operations: string[];
  thematicUnits: string[];
  language: string[];
};

type FilterCheckboxType = {
  label: string;
  value: string;
};

const FILTERS_CHECKBOXES: Record<keyof FiltersType, FilterCheckboxType[]> = {
  class: [
    { label: '1st Grade', value: '1' },
    { label: '2nd Grade', value: '2' },
    { label: '3rd Grade', value: '3' },
    { label: '4th Grade', value: '4' },
    { label: '5th Grade', value: '5' },
    { label: '6th Grade', value: '6' },
    { label: 'Middle School', value: '7' },
    { label: 'High School', value: '8' },
  ],

  operations: [
    { label: 'Additions ', value: 'additions' },
    { label: 'Subtractions ', value: 'subtractions' },
    { label: 'Multiplications ', value: 'multiplications' },
    { label: 'Divisions ', value: 'divisions' },
  ],
  thematicUnits: [
    { label: 'Mental operations', value: 'mental-operations' },
    { label: 'Vertical operations', value: 'vertical-operations' },
  ],
  language: [
    { label: 'English', value: 'en' },
    { label: 'Greek', value: 'el' },
  ],
};

const BOOK_PRODUCTS = [
  {
    stripeId: '1',
    kind: 'ebook',
    name: 'Horizontal Divisions',
    images: ['/images/store/bookCover.png'],
    price: {
      amount: 8000,
      currency: 'usd',
    },
  },
  {
    stripeId: '2',
    kind: 'ebook',
    name: 'Vertical Divisions',
    images: ['/images/store/bookCover.png'],
    price: {
      amount: 12000,
      currency: 'usd',
    },
  },
  {
    stripeId: '3',
    kind: 'ebook',
    name: 'Horizontal Multiplications',
    images: ['/images/store/bookCover.png'],
    price: {
      amount: 80023320,
      currency: 'usd',
    },
  },
  {
    stripeId: '4',
    kind: 'ebook',
    name: 'Vertical Multiplications',
    images: ['/images/store/bookCover.png'],
    price: {
      amount: 81111000,
      currency: 'usd',
    },
  },
  {
    stripeId: '5',
    kind: 'ebook',
    name: 'Horizontal Subtractions',
    images: ['/images/store/bookCover.png'],
    price: {
      amount: 8000,
      currency: 'usd',
    },
  },
  {
    stripeId: '6',
    kind: 'ebook',
    name: 'Vertical Subtractions',
    images: ['/images/store/bookCover.png'],
    price: {
      amount: 12000,
      currency: 'usd',
    },
  },
  {
    stripeId: '7',
    kind: 'ebook',
    name: 'Horizontal Additions',
    images: ['/images/store/bookCover.png'],
    price: {
      amount: 8000,
      currency: 'usd',
    },
  },
  {
    stripeId: '8',
    kind: 'ebook',
    name: 'Vertical Additions',
    images: ['/images/store/bookCover.png'],
    price: {
      amount: 12000,
      currency: 'usd',
    },
  },
  {
    stripeId: '9',
    kind: 'ebook',
    name: 'Horizontal Additions',
    images: ['/images/store/bookCover.png'],
    price: {
      amount: 12000,
      currency: 'usd',
    },
  },
  {
    stripeId: '10',
    kind: 'ebook',
    name: 'Vertical Additions',
    images: ['/images/store/bookCover.png'],
    price: {
      amount: 12000,
      currency: 'usd',
    },
  },
  {
    stripeId: '11',
    kind: 'ebook',
    name: 'Vertical Additions',
    images: ['/images/store/bookCover.png'],
    price: {
      amount: 12000,
      currency: 'usd',
    },
  },
  {
    stripeId: '12',
    kind: 'ebook',
    name: 'Vertical Additions',
    images: ['/images/store/bookCover.png'],
    price: {
      amount: 12000,
      currency: 'usd',
    },
  },
  {
    stripeId: '13',
    kind: 'ebook',
    name: 'Vertical Additions',
    images: ['/images/store/bookCover.png'],
    price: {
      amount: 12000,
      currency: 'usd',
    },
  },
  {
    stripeId: '14',
    kind: 'ebook',
    name: 'Vertical Additions',
    images: ['/images/store/bookCover.png'],
    price: {
      amount: 12000,
      currency: 'usd',
    },
  },
];

const Ebooks = (): JSX.Element => {
  const [selectedFilters, setSelectedFilters] = useState<FiltersType>({
    class: [],
    operations: [],
    thematicUnits: [],
    language: [],
  });

  const GENERATED_FILTERS = Object.keys(FILTERS_CHECKBOXES).map((filterKey) => {
    const filterKeyCasted = filterKey as keyof FiltersType;

    return (
      <div
        key={filterKey}
        style={{
          marginBottom: 32,
          display: 'flex',
          flexDirection: 'column',
          gap: 8,
        }}
      >
        <Text untranslatedText={filterKey.toUpperCase()} type="body1" mb={8} />

        {FILTERS_CHECKBOXES[filterKeyCasted].map((checkbox) => (
          <div
            key={checkbox.value}
            style={{
              display: 'flex',
              gap: 8,
              alignItems: 'center',
              // width: 300,
              // border: '1px solid red',
            }}
          >
            <Checkbox
              variant="primary"
              value={checkbox.value}
              onChange={() => {
                setSelectedFilters((prev) => ({
                  ...prev,
                  [filterKeyCasted]: prev[filterKeyCasted].includes(
                    checkbox.value
                  )
                    ? prev[filterKeyCasted].filter(
                        (value) => value !== checkbox.value
                      )
                    : [...prev[filterKeyCasted], checkbox.value],
                }));
              }}
              isChecked={selectedFilters[filterKeyCasted].includes(
                checkbox.value
              )}
            />

            <Text untranslatedText={checkbox.label} type="body1" />
          </div>
        ))}
      </div>
    );
  });

  return (
    <BooksLayout
      numberOfSearchResults={null}
      isInformationLoading={false}
      isInformationFetching={false}
      totalNumberOfResults={null}
      listContent={<BooksList books={BOOK_PRODUCTS} />}
      filters={GENERATED_FILTERS}
    />
  );
};

export default Ebooks;
