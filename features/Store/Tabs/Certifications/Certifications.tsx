/* eslint-disable react/no-array-index-key */
import { Skeleton } from '@mantine/core';
import { useQuery } from '@tanstack/react-query';
import { useTranslation } from 'react-i18next';

import { PRODUCTS } from '@/common/consts';
import QUERY_KEYS from '@/common/queryKeys';
import CertificationBuyCard from '@/components/CertificationCards/CertificationBuyCard/CertificationBuyCard';
import CertificationProgress from '@/components/CertificationCards/CertificationProgress/CertificationProgress';
import { CartContext } from '@/context/CartProvider';
import PRODUCTS_FROM_SERVICES from '@/services/products';
import { CertificationsProductsType } from '@/types/common';

import s from './Certifications.module.css';

const Certifications = (): JSX.Element => {
  const { t } = useTranslation();
  const { addToCart, isItemInCart, removeFromCart } = CartContext();

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  const { data, error, isError, isFetching } =
    useQuery<CertificationsProductsType | null>({
      queryFn: PRODUCTS_FROM_SERVICES.GET_ALL_CERTIFICATION_PRODUCTS,
      queryKey: [QUERY_KEYS.CERTIFICATION_PRODUCTS],
      staleTime: 1000 * 60 * 60 * 24,
    });

  // TODO : handle the error

  const sortedCertificates = data?.sort(
    (a, b) => (b.progress ? 1 : 0) - (a.progress ? 1 : 0)
  );

  return (
    <div className={s.wrapper}>
      {isFetching
        ? Array(10)
            .fill(0)
            .map((_, index) => (
              <Skeleton
                key={`certification-skeleton-${index}`}
                height={150}
                radius="lg"
              />
            ))
        : sortedCertificates?.map((certificate) => {
            const isCertificatePurchased = Boolean(certificate.progress);
            const isCertificationCompleted =
              certificate?.progress?.modules?.every(
                (module) => module.answered === module.count
              );

            const modulesProgress = !isCertificationCompleted
              ? certificate.progress?.modules.map((module) => ({
                  id: `${module.order}`,
                  progress: Math.round(
                    (Number(module?.answered || 0) * 100) /
                      Number(module?.count || 1)
                  ),
                })) || []
              : [];

            const numberOfCompletedModules = modulesProgress?.filter(
              (module) => module.progress === 100
            ).length;

            return isCertificatePurchased ? (
              <CertificationProgress
                key={certificate.type}
                title={PRODUCTS[certificate.type].typeName}
                description={
                  isCertificationCompleted
                    ? t('complete-certificate-description')
                    : `${t('buy-certificates-description')} ${PRODUCTS[certificate.type].typeName}`
                }
                image={`/images/tests/${PRODUCTS[certificate.type][isCertificationCompleted ? 'certificateCompleteImage' : 'certificateImage']}`}
                steps={modulesProgress}
                numberOfCompletedSteps={numberOfCompletedModules}
                isCompleted={isCertificationCompleted}
                padding="sm"
              />
            ) : (
              <CertificationBuyCard
                key={certificate.type}
                productDetails={{
                  stripeId: certificate.stripeId,
                  kind: 'certificate',
                  // active: certificate.active,
                  type: certificate.type,
                  name: PRODUCTS[certificate.type].typeName,
                  images: certificate.images,
                  price: {
                    amount: certificate.price.amount,
                    currency: certificate.price.currency,
                    totalAmount: certificate.price.totalAmount,
                    taxAmount: certificate.price.taxAmount,
                    taxRate: certificate.price.taxRate,
                  },
                }}
                isAddedToCart={isItemInCart(certificate.stripeId)}
                onAddToCart={() => addToCart(certificate)}
                onRemoveFromCart={() => removeFromCart(certificate.stripeId)}
              />
            );
          })}
    </div>
  );
};

export default Certifications;
