import * as Sentry from '@sentry/nextjs';
import axios from 'axios';

import BASE_ENDPOINT_PATHS from '@/common/baseEndpoints';
import { HTTP_STATUS_CODES } from '@/common/consts';
import { GLOBAL_ERRORS, USERS_ERRORS } from '@/common/errors';
import request from '@/modules/api';
import { UserDetailsType } from '@/types/common';

// * all error Handling is done here from the FE side - its according to swagger
const putProfiles = async (
  // TODO fix any here, any is inserted because the payload type is different in according to billing
  data: any
): Promise<UserDetailsType['profile'] | null> => {
  const res = await request(BASE_ENDPOINT_PATHS.PROFILES, 'PUT', {
    // TODO Fix payload according to occasions details or billing type
    ...(data.firstName && { firstName: data.firstName }),
    ...(data.lastName && { lastName: data.lastName }),
    ...(data.dateOfBirth && { dob: data.dateOfBirth }),
    ...(data.phoneNumber && { phoneNumber: data.phoneNumber }),
    ...(data.organisation && { organisation: data.organisation }),
    ...(data.affiliation && { affiliation: data.affiliation }),
    ...(data.profilePicture && { profilePicture: data.profilePicture }),
    vatNumber: data.vatNumber,
    ...(data.address && {
      address: {
        addressLine1: data.address.addressLine1,
        city: data.address.city,
        postcode: data.address.postcode,
        country: data.address.country,
      },
    }),
  });

  if (res.hasError) {
    switch (res.status) {
      case HTTP_STATUS_CODES.NOT_FOUND:
        throw new Error(USERS_ERRORS.USER_NOT_FOUND);
      case HTTP_STATUS_CODES.BAD_REQUEST:
        if (res.errors?.includes('Tax ID is invalid')) {
          throw new Error(USERS_ERRORS.INVALID_TAX_ID);
        }

        throw new Error(GLOBAL_ERRORS.UNEXPECTED_ERROR);

      default:
        throw new Error(GLOBAL_ERRORS.UNEXPECTED_ERROR);
    }
  }

  return res.data;
};

// * all error Handling is done here from the FE side - its according to swagger
const putProfilesOnlyLanguage = async (
  language: string
): Promise<UserDetailsType['profile'] | null> => {
  const res = await request(BASE_ENDPOINT_PATHS.PROFILES, 'PUT', {
    language,
  });

  if (res.hasError) {
    switch (res.status) {
      case HTTP_STATUS_CODES.NOT_FOUND:
        throw new Error(USERS_ERRORS.USER_NOT_FOUND);
      default:
        throw new Error(GLOBAL_ERRORS.UNEXPECTED_ERROR);
    }
  }

  return res.data;
};

// * all error Handling is done here from the FE side - its according to swagger
const patchProfilesCompleteOnBoard = async () => {
  const res = await request(
    BASE_ENDPOINT_PATHS.PROFILES_COMPLETE_ON_BOARD,
    'PATCH'
  );

  if (res.hasError) {
    switch (res.status) {
      case HTTP_STATUS_CODES.NOT_FOUND:
        throw new Error(USERS_ERRORS.USER_NOT_FOUND);
      default:
        throw new Error(GLOBAL_ERRORS.UNEXPECTED_ERROR);
    }
  }

  return res.data;
};

const postGetUploadUrl = async ({
  profileImage,
}: {
  profileImage: {
    fileName: string;
    imageFile: File;
  };
}) => {
  const res = await request(BASE_ENDPOINT_PATHS.PROFILE_IMAGE_URL, 'POST', {
    filename: profileImage.fileName,
  });

  if (res.hasError) {
    throw new Error(GLOBAL_ERRORS.UNEXPECTED_ERROR);
  }

  const resUpload = await axios.put(
    res.data.url || '  ',
    profileImage.imageFile,
    {
      headers: {
        'x-amz-acl': 'public-read',
      },
      withCredentials: false,
      maxBodyLength: Infinity,
    }
  );

  if (axios.isAxiosError(resUpload)) {
    Sentry.captureException(resUpload);
  }

  if (resUpload.status !== HTTP_STATUS_CODES.SUCCESS) {
    throw new Error(GLOBAL_ERRORS.UNEXPECTED_ERROR);
  }

  const profileImageUrl = res.data.url.split('?')[0] || '';

  return profileImageUrl;
};

const PROFILES = {
  UPDATE_USER_PROFILE: putProfiles,
  COMPLETE_ON_BOARD: patchProfilesCompleteOnBoard,
  UPDATE_USER_LANGUAGE: putProfilesOnlyLanguage,
  UPLOAD_PROFILE_PICTURE: postGetUploadUrl,
};

export default PROFILES;
