import BASE_ENDPOINT_PATHS from '@/common/baseEndpoints';
import { HTTP_STATUS_CODES } from '@/common/consts';
import { GLOBAL_ERRORS, PURCHASES_ERRORS } from '@/common/errors';
import request from '@/modules/api';
import {
  CertificationsProductsType,
  LicensesProductsType,
} from '@/types/common';
import {
  CERTIFICATIONS_PRODUCTS_SCHEMA,
  LICENSES_PRODUCT_SCHEMA,
  PRODUCT_DETAILS_SCHEMA,
} from '@/zod/responseSchemas/products';

// * all error Handling is done here from the FE side - its according to swagger
const getProductsCertificates =
  async (): Promise<CertificationsProductsType | null> => {
    const res = await request(
      BASE_ENDPOINT_PATHS.PRODUCTS_CERTIFICATES,
      'GET',
      {},
      CERTIFICATIONS_PRODUCTS_SCHEMA
    );

    // TODO - Remind BE to explain the error codes - Not in swagger
    if (res.hasError) {
      throw new Error('unexpected-error');
    }

    return res.data;
  };

// * all error Handling is done here from the FE side - its according to swagger
const getProductProductId = async ({
  productId,
}: {
  productId: string;
  country?: string;
  vat?: string;
}) => {
  const res = await request(
    `${BASE_ENDPOINT_PATHS.PRODUCTS}/${productId}`,
    'GET',
    {},
    PRODUCT_DETAILS_SCHEMA
  );

  if (res.hasError) {
    switch (res.status) {
      case HTTP_STATUS_CODES.NOT_FOUND:
        throw new Error(PURCHASES_ERRORS.PRODUCT_NOT_FOUND);
      default:
        throw new Error(GLOBAL_ERRORS.UNEXPECTED_ERROR);
    }
  }

  return res.data;
};

const getProductsLicenses = async (): Promise<LicensesProductsType | null> => {
  const res = await request(
    BASE_ENDPOINT_PATHS.PRODUCTS_LICENSES,
    'GET',
    {},
    LICENSES_PRODUCT_SCHEMA
  );

  // TODO - Remind BE to explain the error codes - Not in swagger
  if (res.hasError) {
    throw new Error('unexpected-error');
  }

  return res.data;
};

const PRODUCTS = {
  GET_ALL_CERTIFICATION_PRODUCTS: getProductsCertificates,
  GET_PRODUCT_DETAILS: getProductProductId,
  GET_ALL_LICENSE_PRODUCTS: getProductsLicenses,
};

export default PRODUCTS;
