import BASE_ENDPOINT_PATHS from '@/common/baseEndpoints';
import { GL<PERSON><PERSON>L_PAGINATION_FETCH_LIMIT } from '@/common/consts';
import { GLOBAL_ERRORS } from '@/common/errors';
import { buildQueryParams } from '@/common/helpers';
import request from '@/modules/api';
import { AdminTestsPaginatedResponseType } from '@/types/common';
import { AdminTestsQueryParamsStateType } from '@/types/queryParams/adminTests';
import { ADMIN_TESTS_LIST_SCHEMA } from '@/zod/responseSchemas/admin/studies';

const getAdminTests = async (
  queryParams: AdminTestsQueryParamsStateType
): Promise<AdminTestsPaginatedResponseType | null> => {
  const { activePage, searchWord, testsCountry } = queryParams || {};

  const queryParameters = {
    ...(searchWord && { query: searchWord }),
    ...(activePage && { page: activePage }),
    ...(testsCountry && { country: testsCountry }),
    limit: GLOBAL_PAGINATION_FETCH_LIMIT,
  };

  const queryString = buildQueryParams(queryParameters);

  const res = await request(
    `${BASE_ENDPOINT_PATHS.ADMIN_TESTS}${queryString ? `?${queryString}` : ''}`,
    'GET',
    {},
    ADMIN_TESTS_LIST_SCHEMA
  );

  if (res.hasError) {
    throw new Error(GLOBAL_ERRORS.UNEXPECTED_ERROR);
  }

  return res.data;
};

const getAdminTestsCountries = async (): Promise<string[] | null> => {
  const res = await request(
    `${BASE_ENDPOINT_PATHS.ADMIN_TESTS}/countries`,
    'GET'
  );

  if (res.hasError) {
    throw new Error(GLOBAL_ERRORS.UNEXPECTED_ERROR);
  }

  return res.data;
};

const ADMIN_TESTS = {
  GET_TESTS: getAdminTests,
  GET_TESTS_COUNTRIES: getAdminTestsCountries,
};

export default ADMIN_TESTS;
