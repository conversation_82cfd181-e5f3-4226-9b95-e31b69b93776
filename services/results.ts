import axios from 'axios';

import BASE_ENDPOINT_PATHS from '@/common/baseEndpoints';
import { HTTP_STATUS_CODES } from '@/common/consts';
import { GLOBAL_ERRORS, TEST_SESSION_REPORT_ERRORS } from '@/common/errors';
import request from '@/modules/api';
import { TestScoresResultsType } from '@/types/common';
import {
  TEST_ERROR_RESULTS_SCHEMA,
  TEST_SCORES_RESULT_SCHEMA,
} from '@/zod/responseSchemas/results';

const BASE_API_URL = process.env.NEXT_PUBLIC_BASE_API_URL;

const getResultsSessionSessionIdScores = async ({
  testId,
}: {
  testId: string;
}): Promise<TestScoresResultsType | null> => {
  if (!testId) return null;

  const response = await request(
    `${BASE_ENDPOINT_PATHS.RESULTS}/${testId}/scores`,
    'GET',
    {},
    TEST_SCORES_RESULT_SCHEMA
  );

  if (response.hasError) {
    switch (response.status) {
      case HTTP_STATUS_CODES.NOT_FOUND:
        throw new Error(TEST_SESSION_REPORT_ERRORS.NO_SESSION_ID_FOUND);
      case HTTP_STATUS_CODES.BAD_REQUEST:
        throw new Error(GLOBAL_ERRORS.BAD_REQUEST);
      default:
        throw new Error(GLOBAL_ERRORS.UNEXPECTED_ERROR);
    }
  }

  return response.data;
};

const getResultsSessionsSessionIdErrors = async ({
  limit,
  page,
  testId,
}: {
  testId: string;
  limit: number;
  page: number;
}) => {
  if (!testId) return null;

  const response = await request(
    `${BASE_ENDPOINT_PATHS.RESULTS}/${testId}/errors?limit=${limit}&page=${page}`,
    'GET',
    {},
    TEST_ERROR_RESULTS_SCHEMA
  );

  if (response.hasError) {
    switch (response.status) {
      case HTTP_STATUS_CODES.NOT_FOUND:
        throw new Error(TEST_SESSION_REPORT_ERRORS.NO_SESSION_ID_FOUND);
      default:
        throw new Error(GLOBAL_ERRORS.UNEXPECTED_ERROR);
    }
  }

  return response.data;
};

const downloadScoresPdf = async ({
  sessionId,
  summary,
}: {
  sessionId: string;
  summary: string;
}): Promise<BlobPart | null> => {
  const fileResponse = await axios.post(
    `${BASE_API_URL}/${BASE_ENDPOINT_PATHS.RESULTS}/${sessionId}/scores/download`,
    { summary },
    {
      headers: {
        'Content-Type': 'application/json',
      },
      withCredentials: true,
      responseType: 'blob',
    }
  );

  if (!fileResponse.data) {
    throw new Error(GLOBAL_ERRORS.UNEXPECTED_ERROR);
  }

  return fileResponse.data;
};

const getResultsSessionSessionIdScoresDownloadOpen = async (
  testId: string
): Promise<{ summary?: string } | null> => {
  if (!testId) return null;

  const response = await request(
    `${BASE_ENDPOINT_PATHS.RESULTS}/${testId}/scores/download/open`
  );

  if (response.hasError) {
    switch (response.status) {
      case HTTP_STATUS_CODES.NOT_FOUND:
        throw new Error(TEST_SESSION_REPORT_ERRORS.NO_SESSION_ID_FOUND);
      case HTTP_STATUS_CODES.BAD_REQUEST:
        throw new Error(GLOBAL_ERRORS.BAD_REQUEST);
      default:
        throw new Error(GLOBAL_ERRORS.UNEXPECTED_ERROR);
    }
  }

  return response.data;
};

const RESULTS = {
  GET_TEST_SCORES_REPORT: getResultsSessionSessionIdScores,
  GET_TEST_REPORT_ERRORS: getResultsSessionsSessionIdErrors,
  GET_SCORES_PDF: downloadScoresPdf,
  GET_TEST_SUMMARY: getResultsSessionSessionIdScoresDownloadOpen,
};

export default RESULTS;
