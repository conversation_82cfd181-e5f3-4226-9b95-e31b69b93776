import BASE_ENDPOINT_PATHS from '@/common/baseEndpoints';
import { HTTP_STATUS_CODES } from '@/common/consts';
import { GLOBAL_ERRORS } from '@/common/errors';
import request from '@/modules/api';
import {
  AllApplicationTestsDetailsType,
  ProductsType,
  SupportedSchoolGradesType,
} from '@/types/common';
import {
  ALL_APPLICATION_TESTS_SCHEMA,
  TEST_DETAILS_SCHEMA,
} from '@/zod/responseSchemas/tests/details';

// * all error Handling is done here from the FE side - its according to swagger
const getTestsTypeAll = async (
  onlyAvailable = false
): Promise<AllApplicationTestsDetailsType | null> => {
  const res = await request(
    `${BASE_ENDPOINT_PATHS.ALL_TESTS}${onlyAvailable ? '?filter=available' : ''}`,
    'GET',
    {},
    ALL_APPLICATION_TESTS_SCHEMA
  );

  if (res.hasError) {
    throw new Error(GLOBAL_ERRORS.UNEXPECTED_ERROR);
  }

  return res.data;
};

const getTestsTypeTypeOverview = async (testType: ProductsType) => {
  const res = await request(
    `tests/type/${testType}/overview`,
    'GET',
    {},
    TEST_DETAILS_SCHEMA
  );

  if (res.hasError) {
    switch (res.status) {
      case HTTP_STATUS_CODES.NOT_FOUND:
        throw new Error('test-not-found');
      case HTTP_STATUS_CODES.BAD_REQUEST:
        throw new Error('test-not-found');
      default:
        throw new Error(GLOBAL_ERRORS.UNEXPECTED_ERROR);
    }
  }

  return res.data;
};

const getTestsTypeTypeLanguages = async (testType: ProductsType) => {
  const res = await request(
    `tests/type/${testType}/languages`,
    'GET',
    {}
    // TEST_DETAILS_SCHEMA
  );

  if (res.hasError) {
    switch (res.status) {
      case HTTP_STATUS_CODES.UNAUTHORIZED:
        throw new Error(GLOBAL_ERRORS.UNAUTHORIZED);
      default:
        throw new Error(GLOBAL_ERRORS.UNEXPECTED_ERROR);
    }
  }

  return res.data;
};

const getTestsTypeTypeSubTests = async ({
  grade,
  language,
  testType,
}: {
  testType: ProductsType;
  language?: string;
  grade: SupportedSchoolGradesType | null;
}) => {
  const res = await request(
    `tests/type/${testType}/subtests${`${grade ? `?grade=${grade}` : ''}`}${language ? `&lang=${language}` : ''}`,
    'GET',
    {}
    // TEST_DETAILS_SCHEMA
  );

  if (res.hasError) {
    switch (res.status) {
      case HTTP_STATUS_CODES.UNAUTHORIZED:
        throw new Error(GLOBAL_ERRORS.UNAUTHORIZED);
      default:
        throw new Error(GLOBAL_ERRORS.UNEXPECTED_ERROR);
    }
  }

  return res.data;
};

const TESTS_DETAILS = {
  GET_ALL_APPLICATION_TESTS: getTestsTypeAll,
  GET_TEST_DETAILS: getTestsTypeTypeOverview,
  GET_SUB_TESTS_BY_TEST_TYPE: getTestsTypeTypeSubTests,
  GET_SUPPORTED_LANGUAGES_FOR_TEST_TYPE: getTestsTypeTypeLanguages,
};

export default TESTS_DETAILS;
