export const GLOBAL_ERRORS = {
  UNAUTHORIZED: 'unauthorized',
  UNAUTHORIZED_ACCESS: 'unauthorized-access',
  REQUEST_TIMEOUT: 'request-timeout',
  SERVER_ERROR: 'server-error',
  ZOD_ERROR: 'zod-error',
  UNEXPECTED_ERROR: 'unexpected-error',
  BAD_REQUEST: 'bad-request',
} as const;

export const AUTH_ERRORS = {
  SEND_BAD_DATA: 'check-provided-data',
  NOT_COMPLETE_REGISTRATION: 'not-completed-registration',
  INVALID_USERNAME_OR_PASSWORD: 'invalid-username-or-password',
  REGISTRATION_ALREADY_COMPLETED: 'registration-already-completed',
  INVITATION_ALREADY_USED: 'invitation-already-used',
  INVITATION_NOT_FOUND: 'invitation-not-found',
  INVALID_SIX_DIGIT_CODE: 'invalid-six-digit-code',
  INVALID_PROVIDED_PASSWORD: 'invalid-provided-password',
  LOGIN_STUDY_NOT_FOUND: 'login-study-not-found',
  LOGIN_SCHOOL_NOT_FOUND: 'login-school-not-found',
  TEST_SESSION_NOT_ACTIVE: 'test-session-not-active',
  NOT_VERIFIED_EMAIL: 'not-verified-email',
} as const;

export const SCHOOLS_ERRORS = {
  TEACHER_NOT_FOUND: 'teacher-not-found',
  SCHOOL_STUDENT_CODE_ALREADY_EXISTS: 'student-code-already-exists',
  INVALID_TAX_ID: 'invalid-tax-id',
} as const;

export const STUDENTS_ERRORS = {
  TEACHER_NOT_FOUND: 'teacher-not-found',
  STUDENT_CODE_ALREADY_EXISTS: 'student-code-already-exists',
} as const;

export const CLASSES_ERRORS = {
  CLASS_NOT_FOUND: 'class-not-found',
  UNAUTHORIZED: 'unauthorized',
  BAD_REQUEST: 'bad-request',
  CLASS_EXISTS: 'class-name-exists',
};

export const GROUPS_ERRORS = {
  GROUP_NOT_FOUND: 'group-not-found',
  UNAUTHORIZED: 'unauthorized',
  BAD_REQUEST: 'bad-request',
  GROUP_EXISTS: 'group-name-exists',
};

export const PURCHASES_ERRORS = {
  PRODUCT_NOT_FOUND: 'product-not-found',
  INVALID_VAT_NUMBER: 'invalid-vat-number',
} as const;

export const INVITATIONS_ERRORS = {
  TEACHER_ALREADY_INVITED: 'teacher-already-invited',
  INVALID_TEACHER_INVITATION_DETAILS: 'invalid-teacher-invitation-details',
  INVITATION_NOT_FOUND: 'invitation-not-found',
  USERS_SCHOOL_NOT_FOUND: 'users-school-not-found',
} as const;

export const CERTIFICATE_PROGRESS_ERRORS = {
  CERTIFICATE_COMPLETE: 'certificate-complete',
  MODULE_NOT_CURRENT: 'module-not-current',
  CERTIFICATE_NOT_FOUND: 'certificate-not-found',
  MODULE_NOT_EXIST: 'module-not-exist',
  QUESTION_NOT_EXIST: 'question-not-exist',
  BAD_ANSWERS_FORMAT: 'bad-answers-format',
  ANSWERED_ALREADY: 'question-answered-already',
  PROGRESS_REPORT_FOR_USER_NOT_FOUND: 'progress-report-for-user-not-found',
  NOT_SUPPORTED_TEST_TYPE: 'not-supported-test-type',
} as const;

export const USERS_ERRORS = {
  USER_NOT_FOUND: 'user-not-found',
  USER_ALREADY_EXISTS: 'user-already-exists',
  INVALID_TAX_ID: 'invalid-tax-id',
} as const;

export const TEST_ERRORS = {
  NOT_ENOUGH_LICENSES: 'not-enough-licenses',
  STUDENT_TEST_SESSION_IS_NOT_RUNNING: 'student-test-session-not-running',
  SESSION_ANSWERED_QUESTION_IS_NOT_IN_SYNC_WITH_CURRENT: 'current-out-of-sync',
  // Handle those below ---
  SESSION_ID_DOES_NOT_EXIST_FOR_USER: 'session-id-does-not-exist-for-user',
  SESSION_IS_EITHER_NOT_STARTED_OR_COMPLETED:
    'session-is-either-not-started-or-completed',
  SESSION_IS_ALREADY_STARTED_OR_SCHEDULED_FOR_LATER:
    'session-is-already-started-or-scheduled-for-later',
  //  Till here
};

export const DOWNLOAD_SESSIONS = {
  NO_SESSIONS_TO_DOWNLOAD_FOUND: 'no-sessions-to-download-found',
  SESSION_COUNT_TOO_LARGE: 'session-count-too-large',
};

export const TEST_SESSION_REPORT_ERRORS = {
  NO_SESSION_ID_FOUND: 'no-test-session-id-found',
};

export const ADMIN_STUDIES_ERRORS = {
  STUDY_NAME_EXISTS: 'study-already-exists',
  INVALID_PROVIDED_STUDY_DATA: 'invalid-provided-study-data',
};
