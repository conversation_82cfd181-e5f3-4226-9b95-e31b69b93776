import {
  StripeElementsOptions,
  StripePaymentElementOptions,
} from '@stripe/stripe-js';

import baseTheme from '@/styles/baseTheme';
import { TranslationKeysType } from '@/types/common';

const { colors } = baseTheme;

// must be divided absolutely by 3 and 2 for grid layout
export const GLOBAL_PAGINATION_FETCH_LIMIT = 48;

export const SUPPORTED_LANGUAGES = {
  en: {
    name: 'English',
    code: 'en-GB',
    shortCode: 'en',
    transKey: 'english',
  },
  el: {
    name: 'Ελληνικά',
    code: 'el-GR',
    shortCode: 'el',
    transKey: 'greek',
  },
  tr: {
    name: 'Türkçe',
    code: 'tr-TR',
    shortCode: 'tr',
    transKey: 'turkish',
  },
  sv: {
    name: '<PERSON><PERSON>',
    code: 'sv-SE',
    shortCode: 'sv',
    transKey: 'swedish',
  },
} as const;

export const COMPANY_DETAILS = {
  name: 'MathP<PERSON>',
  address: 'Athens, Greece',
  email: '<EMAIL>',
} as const;

export const HTTP_STATUS_CODES = {
  SUCCESS: 200,
  CREATED: 201,
  ACCEPTED: 202,
  NO_CONTENT: 204,
  BAD_REQUEST: 400,
  UNAUTHORIZED: 401,
  PAYMENT_REQUIRED: 402,
  FORBIDDEN: 403,
  NOT_FOUND: 404,
  METHOD_NOT_ALLOWED: 405,
  NOT_ACCEPTABLE: 406,
  REQUEST_TIMEOUT: 408,
  CONFLICT: 409,
  CONTENT_TOO_LARGE: 413,
  BAD_FORM_DATA: 422,
  TOO_MANY_REQUESTS: 429,
  SERVER_ERROR: 500,
} as const;

export const BLUR_IMAGE_SVG =
  'data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mOsa2yqBwAFCAICLICSyQAAAABJRU5ErkJggg==';

export const CURRENCY_SYMBOLS = {
  usd: '$',
  eur: '€',
  gbp: '£',
};

export const PRODUCTS = {
  'mathpro-s': {
    id: 'mathpro-s',
    typeName: 'MathPro-S',
    testName: 'S-TEST',
    licenseIcon: 'STypeLicenseSvg',
    certificateImage: 'sCertificate.png',
    certificateIcon: 'STypeCertificationSvg',
    completedCertificateIcon: 'STypeCompletedCertificationSvg',
    certificateCompleteImage: 'sCertificateComplete.png',
    testImage: 'sTest.png',
    testLockedImage: 'sTestLocked.png',
    short: 'S',
  },
  'mathpro-d': {
    id: 'mathpro-d',
    typeName: 'MathPro-D',
    testName: 'D-TEST',
    licenseIcon: 'DTypeLicenseSvg',
    certificateImage: 'dCertificate.png',
    certificateIcon: 'DTypeCertificationSvg',
    completedCertificateIcon: 'DTypeCompletedCertificationSvg',
    certificateCompleteImage: 'dCertificateComplete.png',
    testImage: 'dTest.png',
    testLockedImage: 'dTestLocked.png',
    short: 'D',
  },
  'mathpro-h': {
    id: 'mathpro-h',
    typeName: 'MathPro-H',
    testName: 'H-TEST',
    licenseIcon: 'HTypeLicenseSvg',
    certificateImage: 'hCertificate.png',
    certificateIcon: 'HTypeCertificationSvg',
    completedCertificateIcon: 'HTypeCompletedCertificationSvg',
    certificateCompleteImage: 'hCertificateComplete.png',
    testImage: 'hTest.png',
    testLockedImage: 'hTestLocked.png',
    short: 'H',
  },
  'mathpro-q': {
    id: 'mathpro-q',
    typeName: 'MathPro-Q',
    testName: 'Q-TEST',
    licenseIcon: 'QTypeLicenseSvg',
    certificateImage: 'qCertificate.png',
    certificateIcon: 'QTypeCertificationSvg',
    completedCertificateIcon: 'QTypeCompletedCertificationSvg',
    certificateCompleteImage: 'qCertificateComplete.png',
    testImage: 'qTest.png',
    testLockedImage: 'qTestLocked.png',
    short: 'Q',
  },
  'mathpro-r': {
    id: 'mathpro-r',
    typeName: 'MathPro-R',
    testName: 'R-TEST',
    licenseIcon: 'RTypeLicenseSvg',
    certificateImage: 'rCertificate.png',
    certificateIcon: 'RTypeCertificationSvg',
    completedCertificateIcon: 'RTypeCompletedCertificationSvg',
    certificateCompleteImage: 'rCertificateComplete.png',
    testImage: 'rTest.png',
    testLockedImage: 'rTestLocked.png',
    short: 'R',
  },
} as const;

export const MONTHS_BY_NUMBER: {
  [key: number]: {
    short: string;
    long: string;
  };
} = {
  1: {
    short: 'jan',
    long: 'january',
  },
  2: {
    short: 'feb',
    long: 'february',
  },
  3: {
    short: 'mar',
    long: 'march',
  },
  4: {
    short: 'apr',
    long: 'april',
  },
  5: {
    short: 'may',
    long: 'may',
  },
  6: {
    short: 'jun',
    long: 'june',
  },
  7: {
    short: 'jul',
    long: 'july',
  },
  8: {
    short: 'aug',
    long: 'august',
  },
  9: {
    short: 'sep',
    long: 'september',
  },
  10: {
    short: 'oct',
    long: 'october',
  },
  11: {
    short: 'nov',
    long: 'november',
  },
  12: {
    short: 'dec',
    long: 'december',
  },
};

export const paymentElementOptions: StripePaymentElementOptions = {
  layout: 'tabs',
  wallets: {
    applePay: 'never',
    googlePay: 'never',
  },
  fields: {
    billingDetails: {
      address: 'auto',
    },
  },
};

export const paymentInvoiceElementOptions: StripePaymentElementOptions = {
  layout: 'tabs',
  wallets: {
    applePay: 'never',
    googlePay: 'never',
  },
  fields: {
    billingDetails: {
      address: 'if_required',
    },
  },
};

export const appearance: StripeElementsOptions['appearance'] = {
  labels: 'floating',
  rules: {
    '.Error': {
      color: colors.red,
      // marginBottom: '-10px',
    },
    '.Input': {
      backgroundColor: colors.white,
      border: `1px solid ${colors.gray300}`,
      colorText: colors.gray400,
      outline: 'none',
      padding: `10px 16px`,
      boxShadow: `0px 0px 1px ${colors.gray400}`,
    },
    '.Input--invalid': {
      boxShadow: 'none',
      outline: `1px solid ${colors.red}`,
    },
    '.Input:disabled': {
      border: `1px solid ${colors.gray200}`,
    },
    '.Input:focus': {
      border: `1px solid ${colors.blue}`,
      boxShadow: 'none',
      outline: 'none',
    },
    '.Label': {
      color: colors.gray,
      fontWeight: '400',
    },
    '.Tab': {
      backgroundColor: 'white',
    },
    '.TermsText': {
      color: 'transparent',
    },
  },
  theme: 'flat',
  variables: {
    borderRadius: '5px',
    colorBackground: colors.white,
    colorDanger: colors.red,
    colorText: colors.black,
    colorTextPlaceholder: colors.gray400,
    spacingUnit: '4px',
    fontSizeBase: '14px',
  },
};

export const PASSWORD_REQUIREMENTS: {
  re: RegExp;
  label: TranslationKeysType;
}[] = [
  { re: /[0-9]/, label: 'includes-number' },
  { re: /[a-z]/, label: 'includes-lowercase-letter' },
  { re: /[A-Z]/, label: 'includes-uppercase-letter' },
  {
    re: /[$&+,:;=?@#|'<>.^*()%!-]/,
    label: 'includes-special-character',
  },
  { re: /.{8,}/, label: 'includes-min-8-characters' },
];

export const SUPPORTED_SCHOOL_GRADES = [
  '1',
  '2',
  '3',
  '4',
  '5',
  '6',
  '7',
  '8',
  '9',
  '10',
  '11',
  '12',
] as const;

export const SUPPORTED_GENDERS = ['male', 'female'] as const;

export const SQUARE_IMAGE = '/images/squaresCover.png';

export const ENVIRONMENTS = {
  DEVELOPMENT: 'development',
  PRODUCTION: 'production',
  STAGING: 'staging',
} as const;

// prettier-ignore
export const COUNTRIES_ISO_CODES = [
  "AF", "AX", "AL", "DZ", "AS", "AD", "AO", "AI", "AQ", "AG",
  "AR", "AM", "AW", "AU", "AT", "AZ", "BS", "BH", "BD", "BB",
  "BY", "BE", "BZ", "BJ", "BM", "BT", "BO", "BQ", "BA", "BW",
  "BV", "BR", "IO", "BN", "BG", "BF", "BI", "CV", "KH", "CM",
  "CA", "KY", "CF", "TD", "CL", "CN", "CX", "CC", "CO", "KM",
  "CG", "CD", "CK", "CR", "CI", "HR", "CU", "CW", "CY", "CZ",
  "DK", "DJ", "DM", "DO", "EC", "EG", "SV", "GQ", "ER", "EE",
  "SZ", "ET", "FK", "FO", "FJ", "FI", "FR", "GF", "PF", "TF",
  "GA", "GM", "GE", "DE", "GH", "GI", "GR", "GL", "GD", "GP",
  "GU", "GT", "GG", "GN", "GW", "GY", "HT", "HM", "VA", "HN",
  "HK", "HU", "IS", "IN", "ID", "IR", "IQ", "IE", "IM", "IL",
  "IT", "JM", "JP", "JE", "JO", "KZ", "KE", "KI", "KP", "KR",
  "KW", "KG", "LA", "LV", "LB", "LS", "LR", "LY", "LI", "LT",
  "LU", "MO", "MG", "MW", "MY", "MV", "ML", "MT", "MH", "MQ",
  "MR", "MU", "YT", "MX", "FM", "MD", "MC", "MN", "ME", "MS",
  "MA", "MZ", "MM", "NA", "NR", "NP", "NL", "NC", "NZ", "NI",
  "NE", "NG", "NU", "NF", "MK", "MP", "NO", "OM", "PK", "PW",
  "PS", "PA", "PG", "PY", "PE", "PH", "PN", "PL", "PT", "PR",
  "QA", "RE", "RO", "RU", "RW", "BL", "SH", "KN", "LC", "MF",
  "PM", "VC", "WS", "SM", "ST", "SA", "SN", "RS", "SC", "SL",
  "SG", "SX", "SK", "SI", "SB", "SO", "ZA", "GS", "SS", "ES",
  "LK", "SD", "SR", "SJ", "SE", "CH", "SY", "TW", "TJ", "TZ",
  "TH", "TL", "TG", "TK", "TO", "TT", "TN", "TR", "TM", "TC",
  "TV", "UG", "UA", "AE", "GB", "US", "UM", "UY", "UZ", "VU",
  "VE", "VN", "VG", "VI", "WF", "EH", "YE", "ZM", "ZW"
];
// prettier-ignore-end
