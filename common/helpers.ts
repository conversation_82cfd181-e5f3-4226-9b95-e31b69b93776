import { DateInputProps } from '@mantine/dates';
/* eslint-disable no-new */
const convertToDecimal = (value: number) => {
  return value / 100;
};

export const getFormattedAmount = (value: number, fixedIndexes: number = 2) => {
  return convertToDecimal(value).toFixed(fixedIndexes);
};

export const getFormattedPrice = (value: number) => {
  const formattedPrice = getFormattedAmount(value);

  // if formatted price has.00 at the end, remove it
  if (formattedPrice.endsWith('.00')) {
    return formattedPrice.slice(0, -3);
  }
  return formattedPrice;
};

export const convertToInteger = (value: number) => {
  // We multiply by 100 to convert the value to cents and then use toFixed(2) to get precision to 2 decimal places to avoid floating point errors.
  // Over 500000, floating point errors start to occur, so we round the value to the nearest integer using Math.round().
  return Math.round(Number((value * 100).toFixed(2)));
};

export const getFormattedDate = (locale: string, date: string) => {
  return new Date(date).toLocaleDateString(locale, {
    day: 'numeric',
    month: 'numeric',
    year: 'numeric',
  });
};

export const getDateDetails = (date: Date) => {
  const formattedDate = date instanceof Date ? date : new Date(date);

  const day = formattedDate.getDate();
  const month = formattedDate.getMonth() + 1; // Months are 0-indexed
  const year = formattedDate.getFullYear();

  const isToday = formattedDate.toDateString() === new Date().toDateString();

  const hourIn12 = formattedDate.toLocaleString('en-US', {
    hour: 'numeric',
    hour12: true,
  });
  const hourIn24 = formattedDate.toLocaleString('en-US', {
    hour: 'numeric',
    hour12: false,
  });

  const [hourStr, amOrPm] = hourIn12.split(' ');
  const hour = Number(hourStr);
  const minutes = formattedDate.getMinutes();

  return {
    amOrPm: amOrPm?.toLowerCase() || '',
    day,
    hour,
    hourIn24,
    isToday,
    minutes,
    month,
    year,
  };
};

export const getPayloadDate = (date: Date) => {
  const day = date.getDate();
  const month = date.getMonth() + 1;
  const year = date.getFullYear();
  return `${year}-${month}-${day}`;
};

export const padZero = (number: number) => {
  return number < 10 ? `0${number}` : number;
};

export const extractErrorEmailFromBeError = (str: string) => {
  const regex = /"([^"]*)"/;
  const match = str.match(regex);
  return match ? match[1] : null;
};

export const submitFormById = (formId: string) => {
  const form = document.getElementById(formId);

  form?.dispatchEvent(new Event('submit', { bubbles: true, cancelable: true }));
};

export const combineDateTime = (dateString: Date, timeString: string) => {
  // Parse the date and time strings
  const date = new Date(dateString);
  const [hours, minutes] = timeString.split(':').map(Number);

  // Set the hours and minutes on the date object
  date.setHours(hours, minutes, 0, 0);

  // Convert the date to ISO 8601 format in UTC
  return date.toISOString();
};

export const getStudentsDisplayName = ({
  code,
  firstName,
  lastName,
}: {
  firstName?: string;
  lastName?: string;
  code?: string;
}) => {
  if (code && firstName && lastName)
    return `${firstName} ${lastName} (${code})`;

  if (firstName && lastName && !code) return `${firstName} ${lastName}`;

  return code || '';
};

export const isUrl = (str: string) => {
  try {
    new URL(str);
    return true;
  } catch (error) {
    return false;
  }
};

export const mantineDateParser: DateInputProps['dateParser'] = (input) => {
  if (input) {
    const [day, month, year] = input.split('/').map(Number);
    return new Date(year, month - 1, day);
  }

  return null;
};

export const buildQueryParams = <T extends Record<string, unknown>>(
  params: T
): string => {
  const queryParams = new URLSearchParams();

  Object.keys(params).forEach((key) => {
    const value = params[key];

    if (value === undefined || value === null || value === '') return;

    if (Array.isArray(value)) {
      if (value.length > 0) {
        queryParams.append(key, value.join(','));
      }
    } else if (value instanceof Date) {
      queryParams.append(key, getPayloadDate(value));
    } else if (typeof value === 'object') {
      queryParams.append(key, JSON.stringify(value));
    } else {
      queryParams.append(key, String(value));
    }
  });

  return queryParams.toString();
};

export const getSelectedFiltersLength = (obj: Record<string, unknown>) => {
  return Object.values(obj).filter((item) => {
    if (typeof item === 'object' && Array.isArray(item)) return item.length > 0;
    return Boolean(item);
  }).length;
};

export const getFullNameFromFirstAndLastName = (
  firstName?: string,
  lastName?: string
) => {
  return `${firstName || ''} ${lastName || ''}`.trim();
};

export const getRole = (roles: (string | null)[]) => {
  if (roles.includes('admin')) return 'admin';

  if (roles.includes('schoolAdmin')) return 'schoolAdmin';

  if (roles.includes('studyAdmin')) return 'studyAdmin';

  return roles[0] || '-';
};

export const isValidImageUrl = (url: string) => {
  try {
    new URL(url);
    return true;
  } catch (error) {
    return false;
  }
};

export const removeQueryFromUrl = (url: string, queryKey: string): string => {
  // Split URL into path and query
  const [path, query] = url.split('?');

  if (!query) return url;

  // Filter out the target query parameter
  const newQuery = query
    .split('&')
    .filter((param) => !param.startsWith(`${queryKey}=`))
    .join('&');

  return newQuery ? `${path}?${newQuery}` : path;
};
